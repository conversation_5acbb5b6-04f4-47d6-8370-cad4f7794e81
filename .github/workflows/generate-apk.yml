name: Generate APK

on:
  workflow_dispatch:
    inputs:
      store:
        description: 'Select the Store flavor'
        required: true
        default: 'play'
        type: choice
        options:
          - play
          - veri
          - pax
      buildType:
        description: 'Select the Build type'
        required: true
        default: 'debug'
        type: choice
        options:
          - debug
          - release
      env:
        description: 'Select the Environment'
        required: true
        default: 'stg'
        type: choice
        options:
          - dev
          - stg
          - prod

jobs:
  build:
    name: "Build APK ${{ github.event.inputs.store }}-${{ github.event.inputs.buildType }}-${{ github.event.inputs.env }}"
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 11
        uses: actions/setup-java@v4
        with:
          distribution: 'adopt'
          java-version: '11'

      - name: Cache Gradle packages
        uses: actions/cache@v4
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: ${{ runner.os }}-gradle-

      - name: Capitalize first letter of env
        id: set_env
        run: echo "ENV_CAPITALIZED=$(echo ${{ github.event.inputs.env }} | sed 's/.*/\u&/')" >> $GITHUB_ENV

      - name: Build unsigned APK
        run: |
          ./gradlew assemble${{ github.event.inputs.store }}${{ env.ENV_CAPITALIZED }}${{ github.event.inputs.buildType }} --no-daemon

      - name: List APK files
        run: |
          echo "Listing APK files in the expected directory:"
          ls -R app/build/outputs/apk

      - name: Upload APK as artifact
        uses: actions/upload-artifact@v4
        with:
          name: ${{ github.event.inputs.store }}-${{ github.event.inputs.env }}-${{ github.event.inputs.buildType }}-apk
          path: app/build/outputs/apk/${{ github.event.inputs.store }}${{ env.ENV_CAPITALIZED }}/${{ github.event.inputs.buildType }}/app-${{ github.event.inputs.store }}-${{ github.event.inputs.env }}-${{ github.event.inputs.buildType }}${{ github.event.inputs.buildType == 'release' && '-unsigned' || '' }}.apk
          overwrite: true
