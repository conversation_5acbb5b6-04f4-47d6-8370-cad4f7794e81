name: Generate BukuAgen APK

on:
  workflow_dispatch:
    inputs:
      store-flavor:
        description: 'Store flavor to build (e.g., play, veri, pax)'
        required: true
        default: 'play'
      build-variant:
        description: 'Build variant to use (e.g., debug, release)'
        required: true
        default: 'debug'
      environment:
        description: 'Environment flavor to build (e.g., dev, stg, prod)'
        required: true
        default: 'prod'

jobs:
  apk:
    name: Generate APK
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 11
        uses: actions/setup-java@v4
        with:
          java-version: 11
          distribution: 'adopt'

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Configure Gradle distribution
        run: sed -i -e 's/-all.zip/-bin.zip/' gradle/wrapper/gradle-wrapper.properties

      - name: Assemble APK
        run: |
          bash ./gradlew assemble${{ github.event.inputs['store-flavor'] }}${{ github.event.inputs['environment'] }}${{ github.event.inputs['build-variant'] }} --stacktrace

      - name: Conditionally Sign APK
        if: ${{ github.event.inputs['build-variant'] == 'release' }}
        id: sign
        uses: r0adkll/sign-android-release@v1
        with:
          releaseDirectory: app/build/outputs/apk/${{ github.event.inputs['store-flavor'] }}/${{ github.event.inputs['environment'] }}${{ github.event.inputs['build-variant'] }}
          signingKeyBase64: ${{ secrets.SIGNING_KEY }}
          alias: ${{ secrets.RELEASE_KEY_ALIAS }}
          keyStorePassword: ${{ secrets.RELEASE_KEYSTORE_PASSWORD }}
          keyPassword: ${{ secrets.RELEASE_KEY_PASSWORD }}
        env:
          BUILD_TOOLS_VERSION: "33.0.0"

      - name: Upload APK
        uses: actions/upload-artifact@v4
        with:
          name: ${GITHUB_REPOSITORY}-release-${{ github.event.inputs['store-flavor'] }}-${{ github.event.inputs['environment'] }}-${{ github.event.inputs['build-variant'] }}-apk
          path: ${{ steps.sign.outputs.signedReleaseFile || 'app/build/outputs/apk/${{ github.event.inputs['store-flavor'] }}/${{ github.event.inputs['environment'] }}${{ github.event.inputs['build-variant'] }}/app-${{ github.event.inputs['store-flavor'] }}-${{ github.event.inputs['environment'] }}-${{ github.event.inputs['build-variant'] }}.apk' }}
          overwrite: true
