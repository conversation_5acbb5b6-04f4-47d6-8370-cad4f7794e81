<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="CurrencyEditText">
        <attr name="separateCurrencyColor" format="boolean" />
        <attr name="currencyColor" format="reference" />
    </declare-styleable>
    <declare-styleable name="DynamicNumericKeyboard">
        <attr name="Type" format="enum">
            <enum name="Normal" value="1" />
            <enum name="Dynamic" value="0" />
            <enum name="Firebase" value="2"/>
        </attr>
        <attr name="FirstExtraButton" format="integer" />
        <attr name="IsFirstExtraButton" format="boolean" />
        <attr name="SecondExtraButton" format="integer" />
        <attr name="IsSecondExtraButton" format="boolean" />
        <attr name="ThirdExtraButton" format="integer" />
        <attr name="IsThirdExtraButton" format="boolean" />
        <attr name="isViewPin" format="boolean" />
        <attr name="ButtonBackgroundColor" format="reference" />
        <attr name="buttonColor" format="reference" />
    </declare-styleable>

    <declare-styleable name="InvoiceOnboardingItemView">
        <attr name="isDone" format="boolean" />
        <attr name="background" />
        <attr name="iconBackground"  format="reference"/>
        <attr name="TextColor" format="color"  />
        <attr name="title"/>
        <attr name="isDescription" format="boolean"/>
        <attr name="stepNumber" format="string"/>
    </declare-styleable>
</resources>
