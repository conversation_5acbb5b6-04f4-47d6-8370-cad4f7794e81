<resources>

    <style name="Button.Text" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="textAllCaps">false</item>
    </style>

    <style name="FullScreenDialogStyle" parent="Theme.AppCompat.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
    </style>

</resources>
