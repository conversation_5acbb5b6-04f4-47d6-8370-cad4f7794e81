<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/iv_image"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginHorizontal="@dimen/_26dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/BukuTextStyle.Black60.14Regular"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:textAlignment="center"
        app:layout_constraintEnd_toEndOf="@id/iv_image"
        app:layout_constraintStart_toStartOf="@id/iv_image"
        app:layout_constraintTop_toBottomOf="@id/iv_image" />

</androidx.constraintlayout.widget.ConstraintLayout>