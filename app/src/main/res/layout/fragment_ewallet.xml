<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <FrameLayout
            android:id="@+id/fl_top"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/gradient_malibu_to_blue_60"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_marginBottom="@dimen/_12dp"
                android:background="@drawable/bg_rounded_rectangle_white_16dp"
                android:paddingBottom="@dimen/_16dp">

                <com.bukuwarung.ui_component.component.inputview.BukuInputView
                    android:id="@+id/biv_biller"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/_16dp"
                    app:hint="@string/choose"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:title="@string/choose_ewallet" />

                <com.bukuwarung.ui_component.component.inputview.BukuInputView
                    android:id="@+id/biv_customer_number"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="@dimen/dimen_16dp"
                    android:layout_marginStart="@dimen/dimen_16dp"
                    android:layout_marginEnd="@dimen/_8dp"
                    android:visibility="gone"
                    app:hint="@string/enter_phone_number_hint"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/biv_biller"
                    app:title="@string/enter_phone_number" />

                <com.bukuwarung.ui_component.component.inputview.BukuInputView
                    android:id="@+id/biv_amount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/_16dp"
                    android:visibility="gone"
                    app:hint="@string/amount_0"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/biv_customer_number"
                    app:title="@string/enter_the_nominal_topup_balance" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_next"
                    style="@style/ButtonFill.Yellow"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="@dimen/_16dp"
                    android:padding="@dimen/_12dp"
                    android:text="@string/next"
                    android:visibility="gone"
                    app:cornerRadius="@dimen/_8dp"
                    app:layout_constraintTop_toBottomOf="@+id/biv_amount"
                    app:rippleColor="@color/black_40" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </FrameLayout>

        <View
            android:id="@+id/view_grey"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_0dp"
            android:background="@color/black_5"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/fl_top" />

        <TextView
            android:id="@+id/tv_list_title"
            style="@style/SubHeading1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_20dp"
            android:text="@string/pilih_nominal"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/fl_top" />

        <include
            android:id="@+id/include_shimmer"
            layout="@layout/layout_product_shimmer_view"
            android:layout_width="@dimen/dimen_0dp"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/dimen_16dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/fl_top"
            tools:visibility="visible" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_products"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:background="@color/black_5"
            android:visibility="gone"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_list_title"
            tools:visibility="visible" />

        <FrameLayout
            android:id="@+id/fl_recent_and_fav"
            android:layout_width="@dimen/_0dp"
            android:layout_height="600dp"
            android:background="@color/black_5"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_list_title" />

        <ProgressBar
            android:id="@+id/pb_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>