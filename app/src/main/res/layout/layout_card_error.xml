<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/white"
    android:padding="@dimen/_16dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_error_image"
        android:layout_width="192dp"
        android:layout_height="144dp"
        android:layout_marginTop="48dp"
        app:srcCompat="@drawable/ic_card_error"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_error_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/Heading2"
        android:gravity="center"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/iv_error_image"
        app:layout_constraintStart_toStartOf="@id/iv_error_image"
        app:layout_constraintEnd_toEndOf="@id/iv_error_image" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_error_description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:layout_marginTop="@dimen/_8dp"
        style="@style/Body2"
        app:layout_constraintTop_toBottomOf="@id/tv_error_title"
        app:layout_constraintStart_toStartOf="@id/iv_error_image"
        app:layout_constraintEnd_toEndOf="@id/iv_error_image" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_return"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/back"
        android:textAllCaps="false"
        style="@style/ButtonOutline.Black"
        android:layout_marginTop="@dimen/_32dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_error_description"
        app:layout_constraintBottom_toBottomOf="parent" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/group_pop_up_dialog"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="iv_error_image,tv_error_title,tv_error_description,btn_return" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/long_dialog"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        android:padding="@dimen/_8dp"
        tools:visibility="visible">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_card_title"
                    style="@style/Heading2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_32dp"
                    android:text="Informasi Penting"
                    android:gravity="center"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_ld_error_image"
                    android:layout_width="192dp"
                    android:layout_height="144dp"
                    android:layout_marginTop="@dimen/_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_card_title"
                    app:srcCompat="@drawable/ic_pending_alert" />

                <TextView
                    android:id="@+id/tv_title"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_24dp"
                    android:text="Transaksi Pending."
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/iv_ld_error_image" />

                <LinearLayout
                    android:id="@+id/llBulletPoint"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/tv_title"
                    app:layout_constraintTop_toBottomOf="@id/tv_title"/>

                <include
                    android:id="@+id/pending_balance_info"
                    layout="@layout/info_balance_info"
                    android:layout_width="@dimen/_0dp"
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:layout_marginHorizontal="@dimen/_8dp"
                    android:layout_height="wrap_content"
                    android:layout_marginVertical="@dimen/_30dp"
                    app:layout_constraintStart_toStartOf="@id/tv_title"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toTopOf="@id/tv_tnc"
                    app:layout_constraintTop_toBottomOf="@id/llBulletPoint"/>

                <CheckBox
                    android:id="@+id/cb_pending_tnc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:buttonTint="@color/colorPrimary"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tv_tnc" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_tnc"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_4dp"
                    android:layout_marginBottom="16dp"
                    android:text="@string/pending_trnx_tnc_text"
                    android:textColor="@color/black_60"
                    android:textColorLink="@color/blue_90"
                    android:textSize="12sp"
                    app:fontFamily="@font/roboto"
                    app:layout_constraintBottom_toTopOf="@id/btn_ld_return"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/cb_pending_tnc" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_ld_return"
                    style="@style/ButtonFill.Yellow"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_32dp"
                    android:enabled="false"
                    android:text="@string/understand"
                    android:textAllCaps="false"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>