<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    tools:context=".replacement.presentation.camera.ReplacementVideoCaptureActivity">

    <!-- Camera Preview -->
    <androidx.camera.view.PreviewView
        android:id="@+id/viewFinder"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/layout_controls"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Top Controls -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_top_controls"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/black_50"
        android:padding="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btn_back"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="?selectableItemBackgroundBorderless"
            android:padding="4dp"
            android:src="@mipmap/back_white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:text="@string/replacement_video_capture_title"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/btn_back"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Bottom Controls -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_controls"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/black_50"
        android:padding="24dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <TextView
            android:id="@+id/tv_instruction"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:gravity="center"
            android:text="@string/replacement_video_capture_instruction"
            android:textColor="@color/white"
            android:textSize="14sp"
            app:layout_constraintBottom_toTopOf="@id/video_capture_button"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/video_capture_button"
            android:layout_width="wrap_content"
            android:layout_height="56dp"
            android:layout_marginBottom="16dp"
            android:backgroundTint="@color/colorPrimary"
            android:fontFamily="@font/roboto_bold"
            android:paddingStart="32dp"
            android:paddingEnd="32dp"
            android:text="@string/replacement_start_capture"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Recording Indicator -->
    <View
        android:id="@+id/recording_indicator"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:layout_margin="16dp"
        android:background="@drawable/replacement_recording_indicator"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_top_controls"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>
