<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_saldo_item"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/_4dp"
    android:layout_marginVertical="@dimen/_4dp"
    android:background="@drawable/bg_homepage_tile_button"
    android:paddingVertical="@dimen/_8dp"
    android:paddingHorizontal="@dimen/_24dp"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent"
    app:layout_constraintTop_toBottomOf="@+id/tv_empty_message">

    <TextView
        android:id="@+id/tv_name"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_8dp"
        android:textColor="@color/colorPrimary"
        android:textSize="@dimen/_16dp"
        android:lineHeight="24sp"
        android:textFontWeight="700"
        android:fontFamily="@font/roboto"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Top Up" />

    <ImageView
        android:id="@+id/iv_image"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginLeft="@dimen/_8dp"
        app:layout_constraintStart_toEndOf="@+id/tv_name"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/ic_clock" />
</androidx.constraintlayout.widget.ConstraintLayout>