<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/black_5">

    <include
        android:id="@+id/include_tool_bar"
        layout="@layout/layout_activity_title"
        style="@style/Body3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_pay_up"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_8dp"
        android:text="@string/pay_up"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/include_tool_bar" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_month"
        android:layout_width="@dimen/_0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/_8dp"
        android:paddingBottom="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_pay_up"
        tools:itemCount="12"
        tools:listitem="@layout/layout_month_item" />
</androidx.constraintlayout.widget.ConstraintLayout>