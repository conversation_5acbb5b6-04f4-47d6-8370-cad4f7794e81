<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <ImageView
        android:id="@+id/ivNoDeviceRegistered"
        android:layout_width="match_parent"
        android:layout_height="260dp"
        android:scaleType="fitXY"
        android:src="@drawable/ic_no_device_registered"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvLewati"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        android:background="@drawable/bg_solid_white_corner_16dp"
        android:elevation="@dimen/_8dp"
        android:padding="@dimen/_10dp"
        android:text="<PERSON><PERSON>"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:id="@+id/sv_content"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_0dp"
        android:layout_marginBottom="@dimen/_16dp"
        app:layout_constraintBottom_toTopOf="@id/tvRedirectWebpage"
        app:layout_constraintTop_toBottomOf="@+id/ivNoDeviceRegistered">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guide"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintGuide_percent="0.5" />

            <TextView
                android:id="@+id/tvTitle"
                style="@style/Heading2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:gravity="center_horizontal"
                android:text="Saatnya mulai #BebasTambahCuan Pakai EDC BukuAgen!"
                app:layout_constraintTop_toBottomOf="@id/ivNoDeviceRegistered" />

            <ImageView
                android:id="@+id/ivTransfer"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:src="@drawable/ic_transfer"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvTitle" />

            <TextView
                style="@style/Body2"
                android:id="@+id/tvTransfer"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:text="@string/transfer_onboarding"
                app:layout_constraintBottom_toBottomOf="@id/ivTransfer"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/ivTransfer"
                app:layout_constraintTop_toTopOf="@id/ivTransfer" />

            <ImageView
                android:id="@+id/ivCheckBalance"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:src="@drawable/ic_check_balance"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/ivTransfer" />

            <TextView
                android:id="@+id/tvCheckBalance"
                style="@style/Body2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:text="@string/check_balance_onbaording"
                app:layout_constraintBottom_toBottomOf="@id/ivCheckBalance"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/ivTransfer"
                app:layout_constraintTop_toTopOf="@id/ivCheckBalance" />

            <ImageView
                android:id="@+id/ivPkh"
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:src="@drawable/ic_pkh"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/ivCheckBalance" />

            <TextView
                android:id="@+id/tvPkh"
                style="@style/Body2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_8dp"
                android:layout_marginEnd="@dimen/_16dp"
                android:text="@string/pkh_onboarding"
                app:layout_constraintBottom_toBottomOf="@id/ivPkh"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/ivTransfer"
                app:layout_constraintTop_toTopOf="@id/ivPkh" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <TextView
        android:id="@+id/tvRedirectWebpage"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:gravity="center_horizontal"
        android:text="@string/pelajari_lebih_lanjut"
        android:textColor="@color/colorPrimary"
        app:layout_constraintBottom_toTopOf="@+id/btnOne"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <ImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_2dp"
        android:src="@drawable/ic_open_link"
        app:layout_constraintBottom_toBottomOf="@id/tvRedirectWebpage"
        app:layout_constraintStart_toEndOf="@id/tvRedirectWebpage"
        app:layout_constraintTop_toTopOf="@id/tvRedirectWebpage" />


    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnOne"
        style="@style/ButtonFill.Yellow"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:paddingVertical="@dimen/dimen_11dp"
        android:text="Beli EDC"
        android:textAppearance="@style/Heading3"
        app:cornerRadius="@dimen/dimen_10dp"
        app:layout_constraintBottom_toTopOf="@id/btnTwo" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnTwo"
        style="@style/ButtonOutline.Black"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:text="Saya Sudah Beli EDC"
        app:cornerRadius="@dimen/dimen_10dp"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
