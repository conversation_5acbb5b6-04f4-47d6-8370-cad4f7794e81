<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <include
        android:id="@+id/tb_balance_info"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        layout="@layout/widget_toolbar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <include
        android:id="@+id/layout_date"
        layout="@layout/info_balance_item"
        android:layout_height="wrap_content"
        android:layout_width="@dimen/_0dp"
        android:layout_marginTop="@dimen/_24dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tb_balance_info"/>

    <include
        android:id="@+id/saldo_card"
        layout="@layout/info_balance_card"
        android:layout_height="wrap_content"
        android:layout_width="@dimen/_0dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_date"/>

    <include
        android:id="@+id/balance_info"
        layout="@layout/info_balance_info"
        android:layout_height="wrap_content"
        android:layout_width="@dimen/_0dp"
        android:visibility="gone"
        android:layout_marginBottom="@dimen/_18dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/btn_submit"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_submit"
        style="@style/ButtonFill.Yellow"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        android:textAllCaps="false"
        android:text="Lihat Detail Struk"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>