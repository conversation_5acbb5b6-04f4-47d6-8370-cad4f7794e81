<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/payment_tab_gradient_bg">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_input_area"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_8dp"
            android:background="@drawable/bg_rounded_rectangle_white_8dp"
            android:padding="@dimen/_16dp"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tv_area"
                style="@style/SubHeading2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/select_pdam_area"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_choose_area"
                style="@style/Heading3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:drawablePadding="@dimen/_6dp"
                android:hint="@string/select_region"
                app:drawableEndCompat="@drawable/ic_chevron_down"
                app:layout_constraintTop_toBottomOf="@id/tv_area" />

            <TextView
                android:id="@+id/tv_pdam_area"
                style="@style/Body3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:textColor="@color/black_40"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/tv_choose_area" />

            <View
                android:id="@+id/divider"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="@dimen/_8dp"
                android:background="@color/black_10"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_pdam_area" />

            <com.bukuwarung.ui_component.component.inputview.BukuInputView
                android:id="@+id/biv_customer_number"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:visibility="gone"
                app:hint="@string/pdam_hint"
                app:layout_constraintTop_toBottomOf="@+id/divider"
                app:title="@string/customer_number"
                tools:visibility="visible" />

            <com.bukuwarung.ui_component.component.inputview.BukuInputView
                android:id="@+id/biv_number"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dimen_16dp"
                android:visibility="gone"
                app:bottomText="@string/phone_number_for_sending_message"
                app:hint="@string/enter_phone_number_hint"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/biv_customer_number"
                app:title="@string/customer_phone"
                app:titleHint="@string/optional"
                tools:visibility="visible" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_cek"
                style="@style/ButtonFill.Yellow"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:enabled="false"
                android:padding="@dimen/_12dp"
                android:text="@string/bt_cek"
                android:visibility="gone"
                app:cornerRadius="@dimen/_10dp"
                app:layout_constraintTop_toBottomOf="@+id/biv_number"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/vw_divider"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_5dp"
            android:layout_marginTop="@dimen/_12dp"
            android:background="@color/black_5"
            app:layout_constraintTop_toBottomOf="@+id/cl_input_area" />

        <FrameLayout
            android:id="@+id/fl_recent_and_fav"
            android:layout_width="@dimen/_0dp"
            android:layout_height="600dp"
            android:background="@color/black_5"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/vw_divider" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
