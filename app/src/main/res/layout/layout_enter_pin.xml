<?xml version="1.0" encoding="utf-8"?>

<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:elevation="@dimen/_16dp"
    app:cardCornerRadius="@dimen/_8dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_solid_neutral100_corner_8dp"
        android:paddingBottom="1dp">

        <TextView
            android:id="@+id/tv_title"
            style="@style/Heading2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_12dp"
            android:layout_marginEnd="@dimen/_4dp"
            app:layout_constraintEnd_toStartOf="@+id/iv_eye"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="@string/create_pin" />

        <ImageView
            android:id="@+id/iv_eye"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:src="@drawable/ic_eye_close"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_title" />

        <TextView
            android:id="@+id/tv_subtitle"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_4dp"
            android:text="@string/enter_new_pin"
            android:textColor="@color/black_60"
            app:layout_constraintEnd_toEndOf="@id/iv_eye"
            app:layout_constraintStart_toStartOf="@+id/tv_title"
            app:layout_constraintTop_toBottomOf="@+id/tv_title" />

        <androidx.cardview.widget.CardView
            android:id="@+id/cv_pin"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="1dp"
            android:layout_marginTop="@dimen/_12dp"
            android:elevation="@dimen/_16dp"
            app:cardCornerRadius="@dimen/_8dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_subtitle">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/cl_pin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_solid_white_corner_8dp"
                android:paddingHorizontal="@dimen/_12dp"
                android:paddingVertical="@dimen/_16dp">

                <LinearLayout
                    android:id="@+id/ll_pin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:orientation="horizontal"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/tv_slot1"
                        style="@style/SubHeading1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/background_pin_empty"
                        android:paddingVertical="2dp"
                        android:textAlignment="center"
                        tools:text="1" />

                    <TextView
                        android:id="@+id/tv_slot2"
                        style="@style/SubHeading1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_10dp"
                        android:background="@drawable/background_pin_empty"
                        android:paddingVertical="2dp"
                        android:textAlignment="center"
                        tools:text="2" />

                    <TextView
                        android:id="@+id/tv_slot3"
                        style="@style/SubHeading1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_10dp"
                        android:background="@drawable/background_pin_empty"
                        android:paddingVertical="2dp"
                        android:textAlignment="center"
                        tools:text="3" />

                    <TextView
                        android:id="@+id/tv_slot4"
                        style="@style/SubHeading1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_10dp"
                        android:background="@drawable/background_pin_empty"
                        android:paddingVertical="2dp"
                        android:textAlignment="center"
                        tools:text="4" />

                    <TextView
                        android:id="@+id/tv_slot5"
                        style="@style/SubHeading1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_10dp"
                        android:background="@drawable/background_pin_empty"
                        android:paddingVertical="2dp"
                        android:textAlignment="center"
                        tools:text="5" />

                    <TextView
                        android:id="@+id/tv_slot6"
                        style="@style/SubHeading1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_10dp"
                        android:background="@drawable/background_pin_empty"
                        android:paddingVertical="2dp"
                        android:textAlignment="center"
                        tools:text="6" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tv_error"
                    style="@style/Body2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10dp"
                    android:text="PIN yang Anda masukan mengandung unsur pengulangan"
                    android:textAlignment="center"
                    android:textColor="@color/red_60"
                    app:layout_constraintTop_toBottomOf="@+id/ll_pin" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.cardview.widget.CardView>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>