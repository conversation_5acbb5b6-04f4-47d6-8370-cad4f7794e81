<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <include
        android:id="@+id/toolbar"
        layout="@layout/widget_toolbar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ProgressBar
        android:id="@+id/main_loader"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/secureInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        android:background="@drawable/bg_solid_grey100_corner_4dp_stroke_white"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <ImageView
            android:id="@+id/secureInfoImage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_12dp"
            android:layout_marginBottom="@dimen/_12dp"
            android:src="@drawable/vector_secure"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/secureInfoHeading"
            style="@style/SubHeading1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:text="@string/dont_share_your_pin"
            app:layout_constraintBottom_toTopOf="@+id/secureInfoBody"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/secureInfoImage"
            app:layout_constraintTop_toTopOf="@id/secureInfoImage" />

        <TextView
            android:id="@+id/secureInfoBody"
            style="@style/Body3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:layout_marginTop="@dimen/_4dp"
            android:text="@string/keep_your_pin_secret"
            app:layout_constraintBottom_toBottomOf="@+id/secureInfoImage"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/secureInfoImage"
            app:layout_constraintTop_toBottomOf="@+id/secureInfoHeading" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        android:id="@+id/layoutExtPinpad"
        layout="@layout/layout_external_pinpad"
        android:layout_marginTop="@dimen/_32dp"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/secureInfo"
        tools:visibility="visible" />


    <include
        layout="@layout/layout_overlay" />
</androidx.constraintlayout.widget.ConstraintLayout>