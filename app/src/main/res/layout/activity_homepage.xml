<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <include
        android:id="@+id/tb_homepage"
        layout="@layout/toolbar_homepage"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <androidx.core.widget.NestedScrollView
        android:id="@+id/sv_homepage"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/black_5"
        app:layout_constraintTop_toBottomOf="@id/tb_homepage"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:id="@+id/frame_homepage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                android:orientation="vertical" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_need_help"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/Body1"
                android:textStyle="bold"
                android:visibility="gone"
                android:text="Butuh Bantuan?"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/frame_homepage"
                app:layout_constraintBottom_toBottomOf="parent"
                android:layout_margin="@dimen/_24dp"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_contact_cs"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/Body1"
                android:textStyle="bold"
                android:visibility="gone"
                android:text="Hubungi CS"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/frame_homepage"
                android:layout_margin="@dimen/_24dp"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <com.bukuwarung.bluetooth_printer.utils.MovableFloatingActionButton
        android:id="@+id/fb_help"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.cardview.widget.CardView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/white"
            app:cardUseCompatPadding="true"
            android:elevation="@dimen/_8dp"
            app:cardCornerRadius="@dimen/_32dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginVertical="@dimen/_8dp">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_help_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    style="@style/Body1"
                    android:visibility="visible"
                    android:text="Hubungi CS"
                    app:layout_constraintTop_toTopOf="@id/iv_help"
                    app:layout_constraintBottom_toBottomOf="@id/iv_help"
                    app:layout_constraintEnd_toStartOf="@id/iv_help"
                    android:layout_marginEnd="@dimen/_8dp"/>

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_help"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:srcCompat="@drawable/ic_help" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.cardview.widget.CardView>
    </com.bukuwarung.bluetooth_printer.utils.MovableFloatingActionButton>

    <include
        android:id="@+id/include_payment_loading"
        layout="@layout/payment_loading_screen"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>