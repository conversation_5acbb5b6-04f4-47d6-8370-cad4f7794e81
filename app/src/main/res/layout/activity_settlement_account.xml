<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/grey_f2">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/tb_profile"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tv_toolbar_title"
                style="@style/Heading2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:paddingStart="0dp"
                android:paddingEnd="16dp"
                android:text="@string/your_cash_withdraw_account"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.Toolbar>

    <TextView
        android:id="@+id/tvTitle"
        style="@style/Body3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:padding="@dimen/_16dp"
        android:text="@string/main_account_for_disbursement"
        android:textColor="@color/black_80"
        app:layout_constraintTop_toBottomOf="@id/tb_profile" />

    <ProgressBar
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:id="@+id/progressBar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/colorPrimary"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <include
        android:id="@+id/emptyLayout"
        layout="@layout/layout_empty_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTitle" />

    <androidx.constraintlayout.widget.Group
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="visible"
        android:id="@+id/gpSettlementBank"
        app:constraint_referenced_ids="cl_bank_details,tvTitle"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_bank_details"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/blue5"
        android:paddingHorizontal="16dp"
        android:paddingVertical="16dp"
        app:layout_constraintTop_toBottomOf="@id/tvTitle">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_bank_logo"
            android:layout_width="56dp"
            android:layout_height="44dp"
            android:background="@drawable/bg_solid_white_stroke_grey_border_4dp"
            android:padding="1.5dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_bank" />

        <TextView
            android:id="@+id/tv_bank_holder_name"
            style="@style/SubHeading1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_14dp"
            android:ellipsize="end"
            android:maxLines="1"
            app:layout_constraintBottom_toTopOf="@+id/tv_bank_account_number"
            app:layout_constraintEnd_toStartOf="@id/ivMore"
            android:layout_marginEnd="8dp"
            app:layout_constraintStart_toEndOf="@id/iv_bank_logo"
            app:layout_constraintTop_toTopOf="@id/iv_bank_logo"
            tools:text="Dea Clarissa Safitri" />

        <TextView
            android:id="@+id/tv_bank_account_number"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_14dp"
            android:layout_marginTop="@dimen/_2dp"
            android:textColor="@color/black40"
            app:layout_constraintBottom_toBottomOf="@id/iv_bank_logo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_bank_logo"
            app:layout_constraintTop_toBottomOf="@id/tv_bank_holder_name"
            tools:text="Mandiri - 3883134xxxx" />

        <TextView
            android:id="@+id/tvAccountType"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_14dp"
            android:layout_marginTop="@dimen/_2dp"
            android:text="@string/disbursement_account"
            android:textColor="@color/colorPrimary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_bank_logo"
            app:layout_constraintTop_toBottomOf="@id/tv_bank_account_number" />


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivMore"
            android:padding="@dimen/_4dp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_more" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>