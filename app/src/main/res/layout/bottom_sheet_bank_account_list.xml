<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/title_txt"
                style="@style/Heading2"
                android:layout_marginStart="@dimen/_18dp"
                android:layout_marginTop="@dimen/_20dp"
                android:layout_weight="1"
                android:text="@string/choose_account" />

            <ImageView
                android:id="@+id/close_img"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_20dp"
                android:layout_marginEnd="@dimen/_18dp"
                android:src="@drawable/close" />
        </LinearLayout>

        <TextView
            android:id="@+id/refund_info_txt"
            style="@style/SubHeading2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_20dp"
            android:layout_marginTop="@dimen/_4dp"
            android:layout_marginEnd="@dimen/_20dp"
            android:layout_marginBottom="@dimen/_4dp"
            android:text="@string/refund_info"
            android:textColor="@color/red_80" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_bank_accounts"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:overScrollMode="never"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintBottom_toTopOf="@id/add_bank_account_btn"
            app:layout_constraintTop_toBottomOf="@id/title_txt"
            tools:listitem="@layout/item_list_bank_accounts" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/add_bank_account_btn"
            style="@style/ButtonFill"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_16dp"
            android:text="@string/label_add_account"
            android:textAllCaps="false"
            android:textSize="@dimen/text_16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />
    </LinearLayout>
</layout>
