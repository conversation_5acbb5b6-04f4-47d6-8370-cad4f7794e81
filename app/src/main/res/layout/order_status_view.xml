<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_order_status_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_status"
        style="@style/SubHeading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_solid_red5_corner_8dp"
        android:backgroundTint="@color/grey"
        android:drawablePadding="@dimen/_6dp"
        android:gravity="center_vertical"
        android:paddingVertical="@dimen/_8dp"
        android:paddingStart="@dimen/_6dp"
        android:paddingEnd="@dimen/_8dp"
        android:textColor="@color/white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Pengembalian Uang Diproses" />

    <TextView
        android:id="@+id/tv_date"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textColor="@color/white"
        android:layout_marginEnd="@dimen/_8dp"
        app:layout_constraintBottom_toBottomOf="@id/tv_status"
        app:layout_constraintEnd_toStartOf="@+id/tv_edc_tag"
        app:layout_constraintTop_toTopOf="@id/tv_status"
        app:layout_constraintStart_toEndOf="@+id/tv_status"
        android:gravity="end"
        tools:text="9 Jan 2023, 13:10" />

    <TextView
        android:id="@+id/tv_edc_tag"
        style="@style/Label2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_rounded_rectangle_white_8dp"
        android:padding="@dimen/_8dp"
        android:text="@string/edc_caps"
        android:textStyle="bold"
        android:textColor="@color/black_40"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tv_status"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_status"
        tools:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>