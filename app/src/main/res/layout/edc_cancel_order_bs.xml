<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_top_rounded_corner"
    android:paddingStart="@dimen/_16dp"
    android:paddingTop="@dimen/_10dp"
    android:paddingEnd="@dimen/_16dp"
    android:paddingBottom="@dimen/_20dp">

    <View
        android:id="@+id/view_close_bar"
        android:layout_width="48dp"
        android:layout_height="3dp"
        android:background="@drawable/background_filled_black10_border_2dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_20dp"
        android:text="@string/cancel_order_title"
        app:layout_constraintTop_toBottomOf="@id/view_close_bar" />

    <TextView
        android:id="@+id/tv_message"
        style="@style/Body2"
        android:text="@string/cancel_order_desciption"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/black_80"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_dismiss"
        style="@style/ButtonOutline.Black"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dimen_38dp"
        android:layout_marginEnd="@dimen/_4dp"
        android:text="@string/back"
        app:layout_constraintEnd_toStartOf="@id/btn_cancel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_message" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_cancel"
        style="@style/ButtonFill.Yellow"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_4dp"
        android:layout_marginTop="@dimen/dimen_38dp"
        android:text="@string/yes_cancel"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btn_dismiss"
        app:layout_constraintTop_toBottomOf="@id/tv_message" />

</androidx.constraintlayout.widget.ConstraintLayout>
