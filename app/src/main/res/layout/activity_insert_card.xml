<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <include
        android:id="@+id/tb_insert_card"
        layout="@layout/widget_toolbar"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_insert_card"
        android:layout_width="wrap_content"
        android:layout_height="144dp"
        android:scaleType="fitCenter"
        android:paddingHorizontal="@dimen/_40dp"
        android:adjustViewBounds="true"
        app:srcCompat="@drawable/card_instruction"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_insert_card_header"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Masukan Kartu"
        style="@style/Heading2"
        android:paddingHorizontal="@dimen/_40dp"
        android:layout_marginTop="@dimen/_16dp"
        android:textAlignment="center"
        app:layout_constraintTop_toBottomOf="@+id/iv_insert_card"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_insert_card_info"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="5dp"
        android:paddingHorizontal="@dimen/_40dp"
        android:textColor="@color/black_60"
        android:text="Silakan masukan kartu ATM Anda"
        style="@style/Body2"
        android:textAlignment="center"
        app:layout_goneMarginTop="@dimen/_8dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_insert_card_header"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <include layout="@layout/layout_overlay" />

</androidx.constraintlayout.widget.ConstraintLayout>