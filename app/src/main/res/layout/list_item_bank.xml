<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?selectableItemBackground"
    android:paddingHorizontal="20dp"
    android:paddingVertical="8dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_bank_logo"
        android:layout_width="56dp"
        android:layout_height="46dp"
        android:background="@drawable/bg_solid_white_stroke_grey_border_4dp"
        android:padding="1.5dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_bank" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_bank_name"
        style="@style/Body1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:textColor="@color/heading_text"
        app:layout_constraintBottom_toTopOf="@id/tv_bank_error"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_bank_logo"
        app:layout_constraintTop_toTopOf="@id/iv_bank_logo"
        tools:text="Bank Amar Indonesia (formerly Anglomas International Bank)" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_bank_error"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:maxLines="1"
        android:textColor="@color/red_40"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tv_bank_name"
        app:layout_constraintTop_toBottomOf="@id/tv_bank_name"
        tools:text="Tidak tersedia" />

</androidx.constraintlayout.widget.ConstraintLayout>
