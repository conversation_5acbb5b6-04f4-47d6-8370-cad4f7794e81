<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <include
        android:id="@+id/include_tool_bar"
        layout="@layout/layout_activity_title"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.bukuwarung.ui_component.component.error_view.BukuErrorView
        android:id="@+id/buku_error_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_tool_bar"
        tools:visibility="visible" />

    <WebView
        android:id="@+id/webView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dimen_0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_tool_bar" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_train_loading"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_tool_bar"
        tools:visibility="visible">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lav_train_loading"
            android:layout_width="200dp"
            android:layout_height="200dp"
            app:layout_constraintBottom_toTopOf="@id/tv_train_loading_title"
            app:layout_constraintDimensionRatio="W,1:1"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            app:lottie_autoPlay="true"
            app:lottie_loop="true"
            app:lottie_rawRes="@raw/train_loading" />

        <TextView
            android:id="@+id/tv_train_loading_title"
            style="@style/Heading2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_12dp"
            android:fontFamily="@font/roboto_bold"
            android:gravity="center"
            android:text="@string/train_loading_title"
            app:layout_constraintBottom_toTopOf="@id/tv_train_loading_message"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/lav_train_loading" />

        <TextView
            android:id="@+id/tv_train_loading_message"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_12dp"
            android:gravity="center"
            android:text="@string/train_loading_message"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_train_loading_title" />

        <TextView
            android:id="@+id/tv_powered_by_kai"
            style="@style/Body3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_12dp"
            android:layout_marginBottom="@dimen/_16dp"
            android:drawablePadding="@dimen/_8dp"
            android:gravity="center"
            android:text="@string/powered_by"
            app:drawableBottomCompat="@mipmap/ic_kai"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>