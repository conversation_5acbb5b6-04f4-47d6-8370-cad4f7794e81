<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:background="@color/white"
    android:layout_height="match_parent">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/welcome_viewpager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/_12dp"
        android:layout_alignParentBottom="true"
        android:foregroundGravity="bottom"
        android:layout_marginBottom="@dimen/_40dp"
        android:layout_marginStart="@dimen/_16dp"
        android:background="@color/transparent_welcome"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:tabBackground="@drawable/selector_welcome_page"
        app:tabGravity="start"
        app:tabIndicatorHeight="0dp"
        app:tabSelectedTextColor="@android:color/transparent"
        app:tabTextColor="@android:color/transparent" />

    <Button
        android:id="@+id/btn_skip"
        android:layout_width="wrap_content"
        android:layout_height="60dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:layout_marginStart="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_8dp"
        android:fontFamily="@font/roboto"
        android:gravity="center"
        android:text="@string/skip"
        style="@style/Button.Text"
        android:textAllCaps="false"
        android:textSize="@dimen/text_16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@id/btn_next"
        app:cornerRadius="@dimen/_8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:rippleColor="@color/black_40" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_next"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:layout_marginStart="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:gravity="center"
        android:drawableStart="@drawable/ic_forward_new"
        android:paddingStart="@dimen/_20dp"
        android:textAllCaps="false"
        android:textColor="@color/black_80"
        android:textSize="@dimen/text_16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@id/btn_login"
        app:backgroundTint="@color/buku_CTA_New"
        app:cornerRadius="@dimen/_8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:rippleColor="@color/black_40" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_login"
        android:layout_width="wrap_content"
        android:layout_height="60dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:layout_marginStart="@dimen/_8dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:gravity="center"
        android:text="@string/login_or_register"
        android:textAllCaps="false"
        android:textColor="@color/black_80"
        android:textSize="@dimen/text_16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:backgroundTint="@color/buku_CTA_New"
        app:cornerRadius="@dimen/_8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:rippleColor="@color/black_40" />

</androidx.constraintlayout.widget.ConstraintLayout>
