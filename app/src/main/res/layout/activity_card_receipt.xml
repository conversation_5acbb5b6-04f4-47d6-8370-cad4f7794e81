<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/payment_tab_gradient_bg">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_close"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:padding="4dp"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="@+id/tv_close"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_close"
        app:srcCompat="@drawable/ic_cross_black_80"
        app:tint="@color/white" />

    <TextView
        android:id="@+id/tv_close"
        style="@style/Heading2"
        android:layout_width="0dp"
        android:layout_height="36dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:gravity="center_vertical"
        android:text="@string/close"
        android:textColor="@color/white"
        app:layout_constraintStart_toEndOf="@+id/iv_close"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/warrantyLayout"
        layout="@layout/layout_edc_warranty_nudge"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="visible"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/tv_close" />

    <ScrollView
        android:id="@+id/sv_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:paddingBottom="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/warrantyLayout">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_20dp"
            android:background="@drawable/bg_solid_blue80_corner_12dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_solid_grey_border_8dp"
                android:backgroundTint="@color/white"
                android:padding="@dimen/_16dp"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/iv_status"
                    android:layout_width="150dp"
                    android:layout_height="150dp"
                    android:src="@mipmap/ic_blue_tick"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tv_payment_status"
                    style="@style/Heading2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:text="@string/balance_check_successful"
                    android:textAlignment="center"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/iv_status" />

                <TextView
                    android:id="@+id/tv_total_payment"
                    style="@style/Heading3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_12dp"
                    android:text="@string/filter_history_saldo"
                    android:textAlignment="center"
                    android:textColor="@color/black40"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_payment_status" />


                <TextView
                    android:id="@+id/tv_pending_info_guarantee"
                    style="@style/Heading3"
                    android:visibility="gone"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:layout_marginBottom="@dimen/_4dp"
                    android:textSize="@dimen/dimen_14sp"
                    android:layout_marginHorizontal="@dimen/_14dp"
                    android:text="@string/pending_information_guarantee"
                    android:textAlignment="center"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_total_payment"/>

                <TextView
                    android:id="@+id/tv_total_payment_amount"
                    style="@style/Heading1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_4dp"
                    android:textAlignment="center"
                    android:textSize="28sp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_pending_info_guarantee"
                    tools:text="Rp1.002.000" />

                <View
                    android:id="@+id/vw_dotted_line1"
                    android:layout_width="0dp"
                    android:layout_height="2dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:background="@drawable/horizontal_dotted_line"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_total_payment_amount" />

                <include
                    android:id="@+id/info"
                    layout="@layout/info_balance_info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@id/vw_dotted_line1"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tv_receipt_heading"
                    style="@style/Heading3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:text="@string/proof_of_customer_transaction"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/info" />

                <include
                    android:id="@+id/include_order_invoice"
                    layout="@layout/layout_order_invoice"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_receipt_heading" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>