<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".replacement.presentation.form.ReplacementFormActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@id/toolbar"
        android:layout_width="0dp"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/iv_back"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="start|center_vertical"
                android:background="?selectableItemBackground"
                android:src="@mipmap/back_white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                style="@style/Heading2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:text="@string/replacement_form_title"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_back"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.appcompat.widget.Toolbar>

    <ScrollView
        android:id="@+id/view_content"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/btn_next"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/layout_device_info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:background="@drawable/replacement_background_lighter_grey_border_rounded"
                android:padding="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/iv_device"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/replacement_dummy_img_device"
                    app:layout_constraintBottom_toBottomOf="@+id/tv_device_title"
                    app:layout_constraintEnd_toStartOf="@+id/tv_device_title"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tv_device_title" />

                <TextView
                    android:id="@+id/tv_device_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:textColor="@color/black_800"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="EDC Saku - [serial number]"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/iv_device"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tv_device_terminal_number_label"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/replacement_terminal_number_label"
                android:textColor="@color/material_grey_600"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layout_device_info" />

            <TextView
                android:id="@+id/tv_device_terminal_number"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:text="1234567890"
                android:textColor="@color/black_80"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_device_terminal_number_label" />

            <TextView
                android:id="@+id/tv_device_serial_number_label"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/replacement_serial_number_label"
                android:textColor="@color/material_grey_600"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_device_terminal_number" />

            <TextView
                android:id="@+id/tv_device_serial_number"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:text="0987654321"
                android:textColor="@color/black_80"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_device_serial_number_label" />

            <TextView
                android:id="@+id/tv_user_id_number_label"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/replacement_user_id_label"
                android:textColor="@color/material_grey_600"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_device_serial_number" />

            <TextView
                android:id="@+id/tv_user_id_number"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:text="6281310101212"
                android:textColor="@color/black_80"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_user_id_number_label" />

            <View
                android:id="@+id/view_divider"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="16dp"
                android:background="@color/black10"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_user_id_number" />

            <TextView
                android:id="@+id/tv_replacement_reason_label"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/replacement_reason_label"
                android:textColor="@color/black_800"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/view_divider" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_replacement_reasons"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="100dp"
                android:orientation="vertical"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_replacement_reason_label"
                app:spanCount="1"
                tools:itemCount="6"
                tools:listitem="@layout/replacement_item_reason"
                tools:scrollbars="vertical"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_other_reason_label"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/replacement_other_reason_label"
                android:textColor="@color/black_800"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/rv_replacement_reasons" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_other_reason"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:visibility="gone"
                app:boxBackgroundMode="outline"
                app:counterEnabled="true"
                app:counterMaxLength="120"
                app:hintEnabled="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_other_reason_label"
                tools:visibility="visible">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_other_reason"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/replacement_other_reason_hint"
                    android:inputType="textMultiLine"
                    android:maxLength="120"
                    android:maxLines="4" />

            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                android:id="@+id/tv_upload_video_device_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/replacement_upload_video_label"
                android:textColor="@color/black_800"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/til_other_reason" />

            <ViewFlipper
                android:id="@+id/vf_upload_video_device"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:animateFirstView="false"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_upload_video_device_label">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layout_upload_video"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="?selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true">

                    <TextView
                        android:id="@+id/tv_upload_video_device"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/replacement_background_darker_grey_border_rounded"
                        android:drawablePadding="8dp"
                        android:padding="16dp"
                        android:textSize="16sp"
                        android:textColor="@color/black_800"
                        android:text="@string/replacement_button_upload"
                        android:textStyle="bold"
                        app:drawableStartCompat="@drawable/replacement_ic_upload"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layout_uploaded_video"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="4dp">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/cv_video_thumbnail"
                        android:layout_width="144dp"
                        android:layout_height="80dp"
                        android:layout_marginEnd="8dp"
                        app:cardCornerRadius="6dp"
                        app:cardElevation="1dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <FrameLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/iv_uploaded_video_content"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop" />

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/iv_video_play_overlay"
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:layout_gravity="center"
                                android:background="@drawable/bg_play_button_overlay"
                                android:padding="8dp"
                                app:srcCompat="@drawable/ic_play_arrow_white"
                                app:tint="@android:color/white" />

                        </FrameLayout>

                    </androidx.cardview.widget.CardView>

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/iv_uploaded_video_delete"
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:layout_marginStart="8dp"
                        android:padding="4dp"
                        app:srcCompat="@drawable/replacement_ic_delete"
                        app:layout_constraintBottom_toBottomOf="@id/cv_video_thumbnail"
                        app:layout_constraintStart_toEndOf="@id/cv_video_thumbnail"
                        app:layout_constraintTop_toTopOf="@id/cv_video_thumbnail" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layout_uploading_video"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="16dp">

                    <ProgressBar
                        android:id="@+id/pb_uploading_video"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:text="Uploading video..."
                        android:textColor="@color/black_800"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/pb_uploading_video"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>
            </ViewFlipper>

            <TextView
                android:id="@+id/tv_upload_photo_device_label"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/replacement_upload_photo_label"
                android:textColor="@color/black_800"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/vf_upload_video_device" />

            <ViewFlipper
                android:id="@+id/vf_upload_photo_device"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_upload_photo_device_label">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layout_upload_photo"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="?selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true">

                    <TextView
                        android:id="@+id/tv_upload_photo_device"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/replacement_background_darker_grey_border_rounded"
                        android:drawablePadding="8dp"
                        android:padding="16dp"
                        android:textSize="16sp"
                        android:text="@string/replacement_button_upload"
                        android:textStyle="bold"
                        android:textColor="@color/black_800"
                        app:drawableStartCompat="@drawable/replacement_ic_upload"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layout_uploaded_photo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="4dp">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/cv_photo_thumbnail"
                        android:layout_width="144dp"
                        android:layout_height="80dp"
                        android:layout_marginEnd="8dp"
                        app:cardCornerRadius="6dp"
                        app:cardElevation="1dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/iv_uploaded_photo_content"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:scaleType="centerCrop" />

                    </androidx.cardview.widget.CardView>

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/iv_uploaded_photo_delete"
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:layout_marginStart="8dp"
                        android:padding="4dp"
                        app:srcCompat="@drawable/replacement_ic_delete"
                        app:layout_constraintBottom_toBottomOf="@id/cv_photo_thumbnail"
                        app:layout_constraintStart_toEndOf="@id/cv_photo_thumbnail"
                        app:layout_constraintTop_toTopOf="@id/cv_photo_thumbnail" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/layout_uploading_photo"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="16dp">

                    <ProgressBar
                        android:id="@+id/pb_uploading_photo"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:text="Uploading photo..."
                        android:textColor="@color/black_800"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@id/pb_uploading_photo"
                        app:layout_constraintTop_toTopOf="parent" />

                </androidx.constraintlayout.widget.ConstraintLayout>
            </ViewFlipper>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_next"
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:layout_margin="16dp"
        android:backgroundTint="@color/bar_dashboard_ppob_3"
        android:fontFamily="@font/roboto_bold"
        android:text="@string/replacement_button_continue"
        android:textAllCaps="false"
        android:textColor="@color/black_800"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/view_content" />

</androidx.constraintlayout.widget.ConstraintLayout>