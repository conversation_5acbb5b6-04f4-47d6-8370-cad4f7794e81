<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="264dp"
    android:maxHeight="264dp"
    android:background="@color/gray_200"
    android:paddingTop="@dimen/_4dp"
    android:orientation="vertical"
    android:visibility="visible">

    <LinearLayout
        android:id="@+id/normalLayout"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@id/extraButton"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.75" />

    <LinearLayout
        android:id="@+id/firebaseLayout"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@id/extraButton"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.75" />

    <GridLayout
        android:id="@+id/dynamicLayout"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:visibility="gone"
        app:layout_constraintEnd_toStartOf="@id/extraButton"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.75" />

    <LinearLayout
        android:id="@+id/extraButton"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.25" />

</androidx.constraintlayout.widget.ConstraintLayout>