<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/blue_60"
    android:paddingBottom="@dimen/_8dp">


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_ppob"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_lihat_semua" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/Label3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_6dp"
        android:ellipsize="end"
        android:maxLines="2"
        android:text="@string/see_all"
        android:textAlignment="center"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="@id/iv_ppob"
        app:layout_constraintStart_toStartOf="@id/iv_ppob"
        app:layout_constraintTop_toBottomOf="@id/iv_ppob" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_promo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/vector_promo" />

</androidx.constraintlayout.widget.ConstraintLayout>