<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:paddingTop="24dp"
    android:paddingBottom="8dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_empty_icon"
        android:layout_width="160dp"
        android:layout_height="160dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_mini_edc" />

    <TextView
        android:id="@+id/tv_empty_title"
        style="@style/Heading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="40dp"
        android:layout_marginTop="14dp"
        android:gravity="center"
        android:paddingHorizontal="24dp"
        android:text="Tidak Ada Riwayat Tersedia"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_empty_icon" />

    <TextView
        android:id="@+id/tv_empty_message"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="40dp"
        android:layout_marginTop="14dp"
        android:gravity="center"
        android:paddingHorizontal="24dp"
        android:text="Yuk beli EDC sekarang! "
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_empty_title" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_empty_cta"
        style="@style/ButtonFill.Yellow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="40dp"
        android:layout_marginTop="14dp"
        android:text="Beli EDC"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_empty_message" />

</androidx.constraintlayout.widget.ConstraintLayout>