<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_title"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Bank Virtual Account" />

    <TextView
        android:id="@+id/tv_sub_title"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toEndOf="@+id/tv_title"
        app:layout_constraintStart_toStartOf="@+id/tv_title"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        tools:text="Memiliki batas minimal transaksi" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_payment_methods"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_sub_title"
        tools:itemCount="2"
        tools:listitem="@layout/layout_ppob_payment_method" />

    <View
        android:layout_width="0dp"
        android:layout_height="4dp"
        android:background="@color/black_5"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rv_payment_methods" />

</androidx.constraintlayout.widget.ConstraintLayout>