<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_order_invoice"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_solid_white_corner_8dp"
    android:padding="16dp">

    <TextView
        android:id="@+id/tv_invoice_proof"
        style="@style/SubHeading1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/proof_of_trx"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_edit"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_4dp"
        android:drawablePadding="@dimen/_6dp"
        android:text="@string/edit"
        android:textColor="@color/blue_60"
        app:drawableRightCompat="@drawable/ic_edit_with_border"
        app:layout_constraintEnd_toStartOf="@id/iv_invoice_collapse"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_invoice_collapse"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="@id/tv_invoice_proof"
        app:layout_constraintEnd_toEndOf="parent"
        android:tag="up"
        app:layout_constraintTop_toTopOf="@id/tv_invoice_proof"
        app:srcCompat="@drawable/ic_chevron_up" />

    <com.bukuwarung.edc.payments.util.OrderInvoice
        android:id="@+id/order_invoice"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        android:layout_marginTop="@dimen/_10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_invoice_proof" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_print"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:backgroundTint="@color/colorPrimary"
        android:gravity="center"
        android:padding="@dimen/_8dp"
        android:text="@string/transaction_print"
        android:textAllCaps="false"
        android:textColor="@color/white"
        app:cornerRadius="8dp"
        app:icon="@drawable/ic_printer"
        app:iconGravity="textStart"
        app:iconTint="@color/white"
        app:iconTintMode="src_in"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/order_invoice" />

</androidx.constraintlayout.widget.ConstraintLayout>