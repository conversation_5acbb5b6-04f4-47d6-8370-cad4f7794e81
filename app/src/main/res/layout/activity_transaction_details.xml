<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/widget_toolbar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintTop_toTopOf="parent"
        app:theme="@style/ToolbarTheme">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/btn_back"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:layout_marginStart="@dimen/_16dp"
                android:src="@mipmap/back_white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_title"
                style="@style/Heading2"
                android:layout_marginStart="@dimen/_16dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@string/transaction_detail_toolbar_title"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/btn_back"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/btnHelp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_16dp"
                android:drawableTop="@drawable/ic_baseline_help_outline"
                android:text="@string/help"
                android:textColor="@color/white"
                app:drawableTint="@color/white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.appcompat.widget.Toolbar>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:backgroundTint="@color/colorPrimary"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/widget_toolbar" />

    <include
        android:id="@+id/layoutEmptyView"
        layout="@layout/layout_empty_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/widget_toolbar" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nsvDetails"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/widget_toolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/_0dp"
            android:background="@color/white">

            <View
                android:id="@+id/viewGradient"
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:background="@drawable/payment_tab_gradient_bg"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/viewBackground"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@color/button_material_light"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/viewGradient" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:background="@drawable/bg_solid_green100_corner_8dp"
                android:padding="@dimen/_8dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/ivStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/vector_tick_white"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvStatus"
                    style="@style/Body3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_8dp"
                    android:text="@string/payment_status_completed"
                    android:textColor="@color/white"
                    app:layout_constraintStart_toEndOf="@id/ivStatus"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tvCurDate"
                style="@style/Body1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/_16dp"
                android:textColor="@color/white"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/clStatus"
                tools:text="10 Feb 2024, 11:53" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clTopDetails"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="-50dp"
                android:background="@drawable/bg_corner_8"
                android:paddingBottom="@dimen/_16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/viewGradient">


                <TextView
                    android:id="@+id/tvPaymentAmount"
                    style="@style/SubHeading1"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:gravity="end"
                    android:maxLines="2"
                    android:text="@string/jumlah_bayar"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvPaymentAmountValue"
                    style="@style/Heading2"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_8dp"
                    android:layout_marginTop="@dimen/_16dp"
                    android:layout_marginEnd="@dimen/_16dp"
                    android:gravity="end"
                    android:maxLines="2"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="22 Jan 2024 16:59" />


                <include
                    android:id="@+id/layoutTransactionType"
                    layout="@layout/info_balance_item"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvPaymentAmount" />

                <include
                    android:id="@+id/layoutDestinationBank"
                    layout="@layout/info_balance_item"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/layoutTransactionType" />

                <include
                    android:id="@+id/layoutRefNumber"
                    layout="@layout/info_balance_item"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/layoutDestinationBank" />

                <include
                    android:id="@+id/layoutCardNumber"
                    layout="@layout/info_balance_item"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/layoutRefNumber" />


            </androidx.constraintlayout.widget.ConstraintLayout>

            <include
                android:id="@+id/info"
                layout="@layout/info_balance_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_16dp"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/clTopDetails"
                tools:visibility="visible" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_16dp"
                android:layout_marginTop="@dimen/_16dp"
                android:layout_marginBottom="@dimen/_16dp"
                android:background="@drawable/bg_solid_white_corner_8dp"
                android:paddingHorizontal="@dimen/_16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/info">

                <TextView
                    android:id="@+id/tvCustomerProof"
                    style="@style/SubHeading1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:text="@string/proof_customer_transaction"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clTransactionsDetails"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:layout_marginBottom="@dimen/_16dp"
                    android:background="@drawable/bg_solid_white_stroke_grey_border_4dp"
                    android:paddingHorizontal="@dimen/_2dp"
                    android:paddingBottom="@dimen/_16dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvCustomerProof">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/receiptLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_16dp"
                        android:background="@color/white"
                        android:paddingHorizontal="@dimen/_14dp"
                        android:paddingBottom="@dimen/_16dp"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:id="@+id/iv_logo"
                            android:layout_width="144dp"
                            android:layout_height="64dp"
                            android:scaleType="center"
                            android:src="@drawable/bersama_logo1"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent" />

                        <TextView
                            android:id="@+id/tvStoreName"
                            style="@style/SubHeading1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_4dp"
                            android:gravity="center"
                            android:textColor="@color/black_40"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/iv_logo"
                            tools:text="@string/stored" />

                        <TextView
                            android:id="@+id/tvStoreAddress"
                            style="@style/Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_4dp"
                            android:ellipsize="end"
                            android:gravity="center"
                            android:maxLines="2"
                            android:textColor="@color/black_40"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/tvStoreName"
                            tools:text="@string/stored" />

                        <View
                            android:id="@+id/vwDivider1"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/_2dp"
                            android:layout_marginTop="@dimen/_8dp"
                            android:background="@drawable/horizontal_dashed_line"
                            app:layout_constraintTop_toBottomOf="@+id/tvStoreAddress" />

                        <androidx.constraintlayout.widget.Guideline
                            android:id="@+id/guideline"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            app:layout_constraintGuide_percent="0.4" />

                        <TextView
                            android:id="@+id/tvDateTitle"
                            style="@style/Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_4dp"
                            android:gravity="center"
                            android:text="@string/time"
                            android:textColor="@color/black_40"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/vwDivider1" />

                        <TextView
                            android:id="@+id/tvDateValue"
                            style="@style/Body2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:gravity="start"
                            android:textColor="@color/black_40"
                            app:layout_constraintBottom_toBottomOf="@+id/tvDateTitle"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/guideline"
                            app:layout_constraintTop_toTopOf="@+id/tvDateTitle"
                            tools:text="31 Jan 2024 15:08" />

                        <TextView
                            android:id="@+id/tvTerminalId"
                            style="@style/Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_4dp"
                            android:gravity="center"
                            android:text="@string/terminal_id"
                            android:textColor="@color/black_40"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/tvDateTitle" />

                        <TextView
                            android:id="@+id/tvTerminalIdValue"
                            style="@style/Body2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:gravity="start"
                            android:textColor="@color/black_40"
                            app:layout_constraintBottom_toBottomOf="@+id/tvTerminalId"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/guideline"
                            app:layout_constraintTop_toTopOf="@+id/tvTerminalId"
                            tools:text="75000001" />

                        <TextView
                            android:id="@+id/tvMerchantId"
                            style="@style/Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_4dp"
                            android:gravity="center"
                            android:text="@string/merchant_id"
                            android:textColor="@color/black_40"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/tvTerminalId" />

                        <TextView
                            android:id="@+id/tvMerchantIdValue"
                            style="@style/Body2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:gravity="start"
                            android:textColor="@color/black_40"
                            app:layout_constraintBottom_toBottomOf="@+id/tvMerchantId"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/guideline"
                            app:layout_constraintTop_toTopOf="@+id/tvMerchantId"
                            tools:text="120001202190005" />


                        <TextView
                            android:id="@+id/tvTraceNumberRcTitle"
                            style="@style/Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_4dp"
                            android:gravity="center"
                            android:text="Trace/RC"
                            android:textColor="@color/black_40"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/tvMerchantId" />

                        <TextView
                            android:id="@+id/tvTraceNumberRcValue"
                            style="@style/Body2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:gravity="start"
                            android:textColor="@color/black_40"
                            app:layout_constraintBottom_toBottomOf="@+id/tvTraceNumberRcTitle"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/guideline"
                            app:layout_constraintTop_toTopOf="@+id/tvTraceNumberRcTitle"
                            tools:text="000001/00" />

                        <TextView
                            android:id="@+id/tvRefNumberTitle"
                            style="@style/Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_4dp"
                            android:gravity="center"
                            android:text="@string/reference_number"
                            android:textColor="@color/black_40"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/tvTraceNumberRcTitle" />

                        <TextView
                            android:id="@+id/tvRefNumberValue"
                            style="@style/Body2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:gravity="start"
                            android:textColor="@color/black_40"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/guideline"
                            app:layout_constraintTop_toTopOf="@+id/tvRefNumberTitle"
                            tools:text="739002" />

                        <View
                            android:id="@+id/vwDivider2"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/_2dp"
                            android:layout_marginTop="@dimen/_8dp"
                            android:background="@drawable/horizontal_dashed_line"
                            app:layout_constraintTop_toBottomOf="@+id/tvRefNumberTitle" />

                        <TextView
                            android:id="@+id/tvCardTypeTitle"
                            style="@style/Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_4dp"
                            android:gravity="center"
                            android:text="@string/card_type"
                            android:textColor="@color/black_40"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/vwDivider2" />

                        <TextView
                            android:id="@+id/tvCardTypeValue"
                            style="@style/Body2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:gravity="start"
                            android:textColor="@color/black_40"
                            app:layout_constraintBottom_toBottomOf="@+id/tvCardTypeTitle"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/guideline"
                            app:layout_constraintTop_toTopOf="@+id/tvCardTypeTitle"
                            tools:text="DEBIT TABUNGAN" />

                        <TextView
                            android:id="@+id/tvPosEntryMode"
                            style="@style/Body2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_4dp"
                            android:gravity="start"
                            android:textColor="@color/black_40"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tvCardTypeTitle"
                            tools:text="chip" />

                        <TextView
                            android:id="@+id/tvCardNumberValue"
                            style="@style/Body2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_4dp"
                            android:gravity="start"
                            android:textColor="@color/black_40"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/guideline"
                            app:layout_constraintTop_toTopOf="@+id/tvPosEntryMode"
                            tools:text="622112******1537" />

                        <TextView
                            android:id="@+id/tvSourceAccountBankTitle"
                            style="@style/Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_4dp"
                            android:gravity="center"
                            android:text="@string/bank_source"
                            android:textColor="@color/black_40"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/tvPosEntryMode" />

                        <TextView
                            android:id="@+id/tvSourceAccountBankValue"
                            style="@style/Body2"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:gravity="start"
                            android:textColor="@color/black_40"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@+id/guideline"
                            app:layout_constraintTop_toTopOf="@+id/tvSourceAccountBankTitle"
                            tools:text="BCA-Huma***-0090**" />

                        <include
                            android:id="@+id/layoutPaymentCode"
                            layout="@layout/layout_transaction_status"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:layout_constraintTop_toBottomOf="@id/tvSourceAccountBankValue" />

                        <View
                            android:id="@+id/vwDivider4"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/_2dp"
                            android:layout_marginTop="@dimen/_8dp"
                            android:background="@drawable/horizontal_dashed_line"
                            app:layout_constraintTop_toBottomOf="@+id/layoutPaymentCode" />

                        <TextView
                            android:id="@+id/tvNoteTitle"
                            style="@style/Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_4dp"
                            android:gravity="center"
                            android:text="@string/news"
                            android:textColor="@color/black_40"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/vwDivider4" />

                        <TextView
                            android:id="@+id/tvNoteValue"
                            style="@style/Body2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_6dp"
                            android:gravity="start"
                            android:text="-"
                            android:textColor="@color/black_40"
                            app:layout_constraintStart_toStartOf="@id/guideline"
                            app:layout_constraintTop_toTopOf="@id/tvNoteTitle" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/clTransactionFooter"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/receiptLayout">

                        <androidx.appcompat.widget.AppCompatImageView
                            android:id="@+id/img_app_logo"
                            android:layout_width="26dp"
                            android:layout_height="@dimen/_32dp"
                            app:layout_constraintBottom_toBottomOf="@+id/tv_footer_link"
                            app:layout_constraintEnd_toStartOf="@id/tv_footer_message"
                            app:layout_constraintHorizontal_chainStyle="packed"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="@+id/tv_footer_message"
                            app:srcCompat="@drawable/buku_agen_receipt" />

                        <TextView
                            android:id="@+id/tv_footer_message"
                            style="@style/Body3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_8dp"
                            android:layout_marginTop="38dp"
                            android:text="@string/made_with_bukuwarung_app"
                            android:textColor="@color/black_60"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toTopOf="@+id/tv_footer_link"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintStart_toEndOf="@id/img_app_logo" />

                        <TextView
                            android:id="@+id/tv_footer_link"
                            style="@style/Body4"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/_8dp"
                            android:text="@string/bukuwarung_url"
                            app:layout_constraintStart_toEndOf="@id/img_app_logo"
                            app:layout_constraintTop_toBottomOf="@id/tv_footer_message" />

                        <TextView
                            android:id="@+id/tv_pending_transaction_footer"
                            style="@style/Body3"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/_8dp"
                            android:lineHeight="@dimen/_20dp"
                            android:text="@string/pending_card_transaction_footer"
                            android:textAlignment="center"
                            android:textColor="@color/black_60"
                            android:textSize="@dimen/_11dp"
                            android:visibility="gone"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toBottomOf="@id/tv_footer_link" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnShare"
                        android:layout_width="45dp"
                        android:layout_height="40dp"
                        android:layout_marginLeft="@dimen/_16dp"
                        android:layout_marginTop="@dimen/_20dp"
                        android:background="@drawable/button_border"
                        android:gravity="center"
                        android:text=""
                        android:textAllCaps="false"
                        android:textColor="@color/white"
                        app:cornerRadius="8dp"
                        app:icon="@drawable/ic_share"
                        app:iconGravity="textStart"
                        app:iconTint="@color/colorPrimary"
                        app:iconTintMode="src_in"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/clTransactionFooter" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnPrint"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/_8dp"
                        android:layout_marginTop="@dimen/_16dp"
                        android:layout_marginEnd="@dimen/_16dp"
                        android:backgroundTint="@color/colorPrimary"
                        android:gravity="center"
                        android:padding="@dimen/_8dp"
                        android:text="@string/print_receipt"
                        android:textAllCaps="false"
                        android:textColor="@color/white"
                        app:cornerRadius="8dp"
                        app:icon="@drawable/ic_printer"
                        app:iconGravity="textStart"
                        app:iconTint="@color/white"
                        app:iconTintMode="src_in"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/btnShare"
                        app:layout_constraintTop_toBottomOf="@id/clTransactionFooter" />


                    <TextView
                        android:id="@+id/tvAutoPrint"
                        style="@style/Label1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/_4dp"
                        android:text="@string/auto_print"
                        android:textAlignment="center"
                        android:textColor="@color/black_60"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/btnPrint"
                        tools:visibility="visible" />
                </androidx.constraintlayout.widget.ConstraintLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.core.widget.NestedScrollView>


</androidx.constraintlayout.widget.ConstraintLayout>