<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_home"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Promo" />

    <TextView
        android:id="@+id/tv_subtitle"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        tools:text="sub title" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/vp_payment_banner"
        android:layout_width="@dimen/_0dp"
        android:layout_height="80dp"
        android:layout_marginTop="@dimen/_8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_subtitle" />

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tb_payment_banner"
        android:layout_width="@dimen/_0dp"
        android:layout_height="@dimen/_6dp"
        android:layout_gravity="bottom"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_12dp"
        android:background="@color/colorGreyLight"
        android:foregroundGravity="bottom"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/vp_payment_banner"
        app:layout_constraintTop_toBottomOf="@id/vp_payment_banner"
        app:tabBackground="@drawable/selector_banner"
        app:tabGravity="center"
        app:tabIndicatorHeight="0dp"
        app:tabPaddingEnd="@dimen/_8dp"
        app:tabPaddingStart="@dimen/_8dp"
        app:tabSelectedTextColor="@android:color/transparent" />
</androidx.constraintlayout.widget.ConstraintLayout>