<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_favourite"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_24dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_favourite_grey_border" />

    <ImageView
        android:id="@+id/iv_category"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_marginStart="@dimen/_14dp"
        android:layout_marginTop="@dimen/_12dp"
        android:padding="@dimen/_2dp"
        app:layout_constraintStart_toEndOf="@+id/iv_favourite"
        app:layout_constraintTop_toTopOf="parent"
        tools:srcCompat="@drawable/ic_favourite_listrik" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/Heading3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_12dp"
        android:maxLines="1"
        android:layout_marginEnd="@dimen/_12dp"
        app:layout_constraintEnd_toStartOf="@id/bt_check"
        app:layout_constraintStart_toEndOf="@id/iv_category"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="8866283891" />

    <TextView
        android:id="@+id/tv_date"
        style="@style/Body3"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_4dp"
        android:layout_marginEnd="@dimen/_12dp"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toStartOf="@+id/bt_check"
        app:layout_constraintStart_toEndOf="@id/iv_category"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        tools:text="9 Nov 2021, 15:30" />

    <TextView
        android:id="@+id/tv_amount"
        style="@style/SubHeading2"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginTop="@dimen/_4dp"
        app:layout_constraintStart_toEndOf="@id/iv_category"
        app:layout_constraintTop_toBottomOf="@+id/tv_date"
        tools:text="Rp101.500" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/bt_check"
        style="@style/ButtonOutline.White"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_20dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:paddingTop="@dimen/_4dp"
        android:paddingBottom="@dimen/_4dp"
        android:text="@string/choose"
        android:textAllCaps="false"
        android:textSize="@dimen/text_14sp"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:rippleColor="@color/black_40" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/br_separator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="tv_amount, bt_check" />

    <View
        android:id="@+id/vw_divider"
        style="@style/Divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginHorizontal="@dimen/_16dp"
        app:layout_constraintTop_toBottomOf="@id/br_separator" />

</androidx.constraintlayout.widget.ConstraintLayout>