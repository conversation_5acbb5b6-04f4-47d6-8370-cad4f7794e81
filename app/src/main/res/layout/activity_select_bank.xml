<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <include
        android:id="@+id/toolbar"
        layout="@layout/widget_toolbar"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.bukuwarung.ui_component.component.ExpandedBulletView
        android:id="@+id/eb_warning_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_20dp"
        android:layout_marginTop="@dimen/_14dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_bank_search"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="20dp"
        android:background="@drawable/drawable_login_input"
        android:drawablePadding="8dp"
        android:hint="@string/search_bank"
        android:inputType="textCapSentences"
        android:maxLines="1"
        android:paddingHorizontal="12dp"
        android:textColor="@color/black"
        android:textColorHint="@color/lightBlack"
        android:visibility="gone"
        app:drawableStartCompat="@drawable/ic_search"
        app:layout_constraintTop_toBottomOf="@id/eb_warning_info"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_search_clear"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@id/et_bank_search"
        app:layout_constraintEnd_toEndOf="@id/et_bank_search"
        app:layout_constraintTop_toTopOf="@id/et_bank_search"
        app:srcCompat="@drawable/ic_close"
        tools:visibility="visible" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_banks"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_bank_search"
        tools:itemCount="3"
        tools:listitem="@layout/list_item_bank" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivEmptyList"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/et_bank_search"
        app:srcCompat="@drawable/ic_empty_list" />

    <TextView
        android:id="@+id/tvEmptyList"
        style="@style/Heading3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/bank_could_not_be_found"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ivEmptyList" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/gpEmptyList"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="ivEmptyList,tvEmptyList"
        tools:visibility="visible" />

    <ProgressBar
        android:id="@+id/pb_banks"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:layout_marginTop="45dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

</androidx.constraintlayout.widget.ConstraintLayout>
