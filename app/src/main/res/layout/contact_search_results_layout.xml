<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_search_results_fragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/tv_add_new_fav_contact"
            style="@style/SubHeading2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/black_5"
            android:paddingHorizontal="@dimen/_16dp"
            android:paddingVertical="@dimen/_8dp"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Simpan “Hani” sebagai pelanggan favorit baru" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_contact_picker"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_add_new_fav_contact"
            tools:listitem="@layout/phonebook_contact_item" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <ProgressBar
        android:id="@+id/progress_bar"
        style="?android:attr/progressBarStyleLarge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="40dp"
        android:indeterminate="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>