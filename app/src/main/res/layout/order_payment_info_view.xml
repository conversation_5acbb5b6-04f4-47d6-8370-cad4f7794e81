<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_solid_white_corner_8dp"
    android:paddingHorizontal="16dp"
    android:paddingVertical="12dp">

    <TextView
        android:id="@+id/tv_payment_detail"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/payment_detail"
        app:layout_constraintEnd_toStartOf="@id/iv_detail_collapse"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_detail_collapse"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_chevron_up" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_details_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        app:layout_constraintTop_toBottomOf="@id/tv_payment_detail">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guide_50v"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5" />

        <TextView
            android:id="@+id/tv_total_received"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/total_received_by_customer"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_total_received_value"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:textColor="@color/black_80"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/guide_50v"
            app:layout_constraintTop_toTopOf="@+id/tv_total_received"
            tools:text="Rp249.000" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/br_total_received"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_total_received, tv_total_received_value" />

        <TextView
            android:id="@+id/tv_fee"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_12dp"
            android:text="@string/platform_fee"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/br_total_received" />

        <TextView
            android:id="@+id/tv_fee_value"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_4dp"
            android:background="@drawable/strike_through"
            android:text="@string/free"
            android:textColor="@color/black_40"
            app:layout_constraintBottom_toBottomOf="@+id/tv_fee"
            app:layout_constraintEnd_toStartOf="@id/tv_discounted_fee_value"
            app:layout_constraintTop_toTopOf="@+id/tv_fee" />

        <TextView
            android:id="@+id/tv_discounted_fee_value"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/free"
            app:layout_constraintBottom_toBottomOf="@+id/tv_fee_value"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_fee_value" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/br_transaction_fee"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_fee, tv_fee_value, tv_discounted_fee_value" />

        <TextView
            android:id="@+id/tv_loyalty_discount"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_12dp"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/br_transaction_fee"
            tools:text="Diskon Level Bronze" />

        <TextView
            android:id="@+id/tv_loyalty_discount_value"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="@+id/tv_loyalty_discount"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_loyalty_discount"
            tools:text="-Rp100" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/grp_loyalty_discount"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="tv_loyalty_discount, tv_loyalty_discount_value" />

        <TextView
            android:id="@+id/tv_total_payment"
            style="@style/SubHeading1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_12dp"
            android:text="@string/label_total_payment"
            android:textColor="@color/black40"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_loyalty_discount" />

        <TextView
            android:id="@+id/tv_total_payment_value"
            style="@style/SubHeading1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:textColor="@color/black_80"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/guide_50v"
            app:layout_constraintTop_toTopOf="@+id/tv_total_payment"
            tools:text="Rp250.000" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/br_total_payment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_total_payment, tv_total_payment_value" />

        <View
            android:id="@+id/vw_divider_1"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="12dp"
            android:background="@color/black_10"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/br_total_payment" />

        <TextView
            android:id="@+id/tv_payment_method"
            style="@style/SubHeading1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_12dp"
            android:text="@string/label_payment_method"
            android:textColor="@color/black40"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/vw_divider_1" />

        <TextView
            android:id="@+id/tv_payment_method_value"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:textColor="@color/black_80"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/guide_50v"
            app:layout_constraintTop_toTopOf="@+id/tv_payment_method"
            tools:text="Saldo BukuWarung" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/br_payment_method"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_payment_method, tv_payment_method_value" />

        <TextView
            android:id="@+id/tv_payment_category"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_12dp"
            android:text="@string/category"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toStartOf="@id/guide_50v"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/br_payment_method" />

        <TextView
            android:id="@+id/tv_payment_category_value"
            style="@style/SubHeading1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:textColor="@color/black_80"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/guide_50v"
            app:layout_constraintTop_toTopOf="@+id/tv_payment_category"
            tools:text="Biaya Operasional" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/br_payment_category"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tv_payment_category, tv_payment_category_value" />

        <View
            android:id="@+id/vw_divider_2"
            android:layout_width="0dp"
            android:layout_height="2dp"
            android:layout_marginTop="12dp"
            android:background="@drawable/horizontal_dotted_line"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/br_payment_category" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/gp_payment_categroy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="tv_payment_category, tv_payment_category_value, vw_divider_2" />

        <TextView
            android:id="@+id/tv_notes"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_12dp"
            android:text="@string/additional_notes"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/vw_divider_2" />

        <TextView
            android:id="@+id/tv_notes_value"
            style="@style/Body2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="@color/black_80"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_notes"
            tools:text="Bayar utang" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>