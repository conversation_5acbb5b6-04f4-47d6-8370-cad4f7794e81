<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/colorPrimary">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:elevation="@dimen/_16dp"
        app:cardCornerRadius="@dimen/_12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout xmlns:app="http://schemas.android.com/apk/res-auto"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/yellow_10"
            android:padding="@dimen/_20dp">

            <TextView
                android:id="@+id/tv_ticker_header"
                style="@style/SubHeading1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/info_seputar_pembayaran"
                android:textColor="@color/yellow_80"
                app:drawableEndCompat="@drawable/ic_cross"
                app:drawableTint="@color/yellow_80"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_ticker_body"
                style="@style/Body3"
                android:layout_width="@dimen/_0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10dp"
                android:marqueeRepeatLimit="marquee_forever"
                android:text="@string/ticker_info_detail"
                android:textColor="@color/black_60"
                app:layout_constraintEnd_toEndOf="@id/tv_ticker_header"
                app:layout_constraintStart_toStartOf="@id/tv_ticker_header"
                app:layout_constraintTop_toBottomOf="@id/tv_ticker_header" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</androidx.constraintlayout.widget.ConstraintLayout>