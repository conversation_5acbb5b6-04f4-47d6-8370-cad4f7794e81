<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/_16dp">

    <ImageView
        android:id="@+id/iv_trx"
        android:layout_width="@dimen/_32dp"
        android:layout_height="@dimen/_32dp"
        android:contentDescription="@string/icon"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@mipmap/ic_check_balance" />

    <TextView
        android:id="@+id/tv_trx_name"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        app:layout_constraintStart_toEndOf="@+id/iv_trx"
        app:layout_constraintTop_toTopOf="@+id/iv_trx"
        tools:text="Cek Saldo" />

    <TextView
        android:id="@+id/tv_trx_amount"
        style="@style/Heading3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:textAlignment="textEnd"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tv_trx_name"
        app:layout_constraintTop_toTopOf="@+id/iv_trx"
        tools:text="Rp1.000.000" />

    <TextView
        android:id="@+id/tv_trx_timestamp"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:textColor="@color/black_40"
        app:layout_constraintStart_toStartOf="@+id/tv_trx_name"
        app:layout_constraintTop_toBottomOf="@+id/tv_trx_name"
        tools:text="10 Feb 2024 11:53" />

    <TextView
        android:id="@+id/tv_trx_status"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:textAlignment="textEnd"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tv_trx_timestamp"
        app:layout_constraintTop_toTopOf="@+id/tv_trx_timestamp"
        tools:text="Berhasil" />

    <TextView
        android:id="@+id/tv_reference_number"
        style="@style/Body3.black40"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_8dp"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintEnd_toStartOf="@id/btnActivateEdc"
        app:layout_constraintStart_toStartOf="@+id/tv_trx_timestamp"
        app:layout_constraintTop_toBottomOf="@+id/tv_trx_timestamp"
        tools:text="Ref. No. 73900243841" />

    <TextView
        android:id="@+id/btnActivateEdc"
        style="@style/SubHeading2"
        android:layout_height="@dimen/_26dp"
        android:layout_marginTop="@dimen/_4dp"
        android:background="@drawable/bg_solid_blue80_corner_12dp"
        android:gravity="center"
        android:backgroundTint="@color/colorPrimary"
        android:paddingStart="@dimen/_16dp"
        android:text="@string/activate_edc"
        android:textColor="@color/white"
        android:visibility="gone"
        tools:visibility="visible"
        app:drawableEndCompat="@drawable/ic_chevron_right"
        app:drawableTint="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_trx_status" />

</androidx.constraintlayout.widget.ConstraintLayout>