<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tvSeparatorDate"
        android:layout_width="wrap_content"
        android:padding="@dimen/_8dp"
        style="@style/SubHeading1"
        android:textColor="@color/black_60"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Hari ini" />

</androidx.constraintlayout.widget.ConstraintLayout>