<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <include
        android:id="@+id/tbCashWithdrawalAccountConfirmation"
        layout="@layout/widget_toolbar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:padding="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tbCashWithdrawalAccountConfirmation"
        tools:visibility="visible">


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_ld_error_image"
            android:layout_width="192dp"
            android:layout_height="144dp"
            android:layout_marginTop="@dimen/_40dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_cash_withdraw" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_card_title"
            style="@style/Heading2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_32dp"
            android:text="@string/konfirmasi_rekening"
            app:layout_constraintEnd_toEndOf="@id/iv_ld_error_image"
            app:layout_constraintStart_toStartOf="@id/iv_ld_error_image"
            app:layout_constraintTop_toBottomOf="@id/iv_ld_error_image" />


        <TextView
            android:id="@+id/tvDescription"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_8dp"
            android:text="@string/untuk_menggunakan_fitur_tarik_tunai_silakan"
            android:textColor="#5C5C5C"
            style="@style/Body2"
            android:layout_marginHorizontal="@dimen/_16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_card_title" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/_16dp"
            android:background="@drawable/bg_solid_white_stroke_grey_border_4dp"
            android:padding="@dimen/_16dp"
            app:layout_constraintTop_toBottomOf="@id/tvDescription">

            <ImageView
                android:id="@+id/ivDefaultBank"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_default_radio"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <TextView
                android:id="@+id/tvBankDetails"
                android:layout_width="0dp"
                app:layout_constraintEnd_toEndOf="parent"
                style="@style/SubHeading1"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                app:layout_constraintStart_toEndOf="@id/ivDefaultBank"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="[Bank Name] - [Holder Name]" />

            <TextView
                android:id="@+id/tvAccountNumber"
                android:layout_width="0dp"
                style="@style/Body2"
                app:layout_constraintEnd_toEndOf="parent"
                android:textColor="@color/black40"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/_16dp"
                app:layout_constraintStart_toEndOf="@id/ivDefaultBank"
                app:layout_constraintTop_toBottomOf="@id/tvBankDetails"
                tools:text="[Bank Name] - [Holder Name]" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnConfirm"
            style="@style/ButtonFill.Yellow"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/_16dp"
            android:enabled="true"
            android:text="@string/save"
            android:textAllCaps="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>