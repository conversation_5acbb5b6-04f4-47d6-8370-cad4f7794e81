<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_rounded_rectangle_white_8dp">

    <TextView
        android:id="@+id/tv_status_transaction"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="@dimen/_12dp"
        android:text="@string/payment_status"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_refresh"
        style="@style/SubHeading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:background="@drawable/rounded_blue_border_rectangle_32dp"
        android:drawablePadding="@dimen/_4dp"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_2dp"
        android:text="@string/refresh"
        android:textColor="@color/blue_80"
        app:drawableStartCompat="@drawable/ic_refresh"
        app:layout_constraintBottom_toBottomOf="@+id/tv_status_transaction"
        app:layout_constraintStart_toEndOf="@+id/tv_status_transaction"
        app:layout_constraintTop_toTopOf="@id/tv_status_transaction" />

    <ImageView
        android:id="@+id/iv_arrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="@dimen/_16dp"
        android:paddingVertical="@dimen/_18dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_chevron_up" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_timeline"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:overScrollMode="never"
        android:paddingEnd="@dimen/_16dp"
        android:paddingBottom="@dimen/_20dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_status_transaction" />

</androidx.constraintlayout.widget.ConstraintLayout>