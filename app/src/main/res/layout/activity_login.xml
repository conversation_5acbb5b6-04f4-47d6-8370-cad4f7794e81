<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/coordinatorLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_login_banner"
        android:layout_width="0dp"
        android:layout_margin="20dp"
        android:layout_height="wrap_content"
        android:scaleType="fitXY"
        app:srcCompat="@drawable/login_banner"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_login_banner_logo"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginTop="-30dp"
        app:srcCompat="@drawable/ic_bukuagen_logo"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/iv_login_banner" />
    
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_login_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        android:gravity="center"
        android:textColor="@color/black_80"
        app:fontFamily="@font/roboto_bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_login_banner_logo"
        android:text="@string/edc_application_title" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_login_subtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="5dp"
        android:gravity="center"
        android:textColor="@color/black_80"
        app:fontFamily="@font/roboto"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_login_title"
        android:text="@string/edc_application_subtitle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_login_input"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Masukkan Nomor HP"
        app:fontFamily="@font/roboto_bold"
        android:textColor="@color/black_80"
        app:layout_constraintStart_toStartOf="@id/iv_login_banner"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/tv_country_code"
        android:layout_width="90dp"
        android:layout_height="50dp"
        android:layout_marginTop="20dp"
        android:text="+62"
        android:background="@drawable/drawable_login_country_code"
        android:paddingTop="10dp"
        android:textSize="16sp"
        android:paddingBottom="10dp"
        android:paddingStart="10dp"
        android:gravity="center_vertical"
        android:drawablePadding="8dp"
        android:enabled="false"
        android:textColor="@color/black_60"
        app:drawableStartCompat="@drawable/ic_flag"
        app:layout_constraintTop_toBottomOf="@id/tv_login_input"
        app:layout_constraintStart_toStartOf="@id/tv_login_input" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/et_login_phone"
        style="@style/Base.Widget.MaterialComponents.TextInputEditText"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_edit_text_enabled_disabled"
        android:hint="825765475285"
        android:textCursorDrawable="@null"
        android:inputType="number"
        android:layout_marginStart="10dp"
        android:maxLength="13"
        android:textColor="@color/black"
        android:textColorHint="@color/lightBlack"
        android:textSize="16sp"
        app:layout_constraintStart_toEndOf="@id/tv_country_code"
        app:layout_constraintEnd_toEndOf="@id/iv_login_banner"
        app:layout_constraintTop_toTopOf="@id/tv_country_code"
        app:layout_constraintBottom_toBottomOf="@id/tv_country_code"/>

    <requestFocus />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_login_prompt"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        app:fontFamily="@font/roboto"
        android:textColor="#595959"
        android:text="@string/edc_phone_number_suggestion_text"
        app:layout_constraintTop_toBottomOf="@id/et_login_phone"
        app:layout_constraintStart_toStartOf="@id/iv_login_banner"
        app:layout_constraintEnd_toEndOf="@id/iv_login_banner" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_warning"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        android:background="@drawable/drawable_login_warning"
        android:drawableStart="@drawable/vector_warning"
        android:drawablePadding="@dimen/_8dp"
        android:paddingStart="@dimen/_8dp"
        android:paddingTop="@dimen/_8dp"
        android:paddingEnd="@dimen/_8dp"
        android:paddingBottom="@dimen/_8dp"
        android:textAlignment="textStart"
        android:textColor="@color/black_60"
        android:visibility="gone"
        app:drawableTint="#FF8383"
        app:layout_constraintStart_toStartOf="@id/iv_login_banner"
        app:layout_constraintEnd_toEndOf="@id/iv_login_banner"
        app:layout_constraintTop_toBottomOf="@id/tv_login_prompt"
        tools:text="Maaf, Anda terlalu banyak melakukan percobaan login. Silakan tunggu beberapa saat sebelum mencoba lagi."
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_login_tnc"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        app:fontFamily="@font/roboto"
        android:textSize="12sp"
        android:textColor="@color/black_60"
        android:textColorLink="@color/blue_90"
        app:layout_constraintBottom_toTopOf="@id/btn_login"
        app:layout_constraintStart_toStartOf="@id/btn_login"
        app:layout_constraintEnd_toEndOf="@id/btn_login"
        android:text="@string/tnc_text" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_login"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:enabled="false"
        android:text="@string/login_or_register"
        style="@style/ButtonFill.Yellow"
        app:layout_constraintBottom_toBottomOf="parent" />

    <ProgressBar
        android:id="@+id/pb_login"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
