<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_ppob_usecase"
        android:layout_width="70dp"
        android:layout_height="40dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_category_name"
        style="@style/Body2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:textColor="@color/black60"
        app:layout_constraintBottom_toTopOf="@id/tv_number"
        app:layout_constraintStart_toEndOf="@+id/iv_ppob_usecase"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Tagihan Listrik" />

    <TextView
        android:id="@+id/tv_number"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tv_category_name"
        app:layout_constraintTop_toBottomOf="@+id/tv_category_name"
        tools:text="86618066539" />

    <ImageView
        android:id="@+id/iv_favourite"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_favourite_grey" />

</androidx.constraintlayout.widget.ConstraintLayout>