<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/payment_tab_gradient_bg">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="@+id/tv_close"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_close"
        app:srcCompat="@drawable/ic_cross_black_80"
        app:tint="@color/white" />

    <TextView
        android:id="@+id/tv_close"
        style="@style/Heading2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:text="@string/close"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/iv_close"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:id="@+id/sv_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/_32dp"
        android:paddingBottom="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_close">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_20dp"
            android:background="@drawable/bg_solid_grey_border_8dp"
            android:backgroundTint="@color/white"
            android:padding="@dimen/_16dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivError"
                android:layout_width="150dp"
                android:layout_height="150dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_payment_failed" />

            <TextView
                android:id="@+id/tvPaymentStatus"
                style="@style/Heading2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:text="@string/failed_status_transfer"
                android:textAlignment="center"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ivError" />

            <TextView
                android:id="@+id/tvPaymentAmount"
                style="@style/Heading1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_16dp"
                android:textAlignment="center"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvPaymentStatus"
                tools:text="Rp 1.000.000" />

            <View
                android:id="@+id/vwDivider"
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_marginTop="@dimen/_16dp"
                android:background="@color/black_5"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvPaymentAmount" />

            <TextView
                android:id="@+id/tvDestinationBankAccount"
                style="@style/Heading3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/destination_account"
                android:layout_marginTop="@dimen/_8dp"
                android:textColor="@color/black40"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/vwDivider" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:padding="@dimen/_8dp"
                android:background="@drawable/bg_rounded_rectangle_blue_5_4dp"
                app:layout_constraintTop_toBottomOf="@+id/tvDestinationBankAccount">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivBankLogo"
                    android:layout_width="56dp"
                    android:layout_height="46dp"
                    android:background="@drawable/bg_solid_white_stroke_grey_border_4dp"
                    android:padding="1.5dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_bank" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvBankName"
                    style="@style/Body1"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/_16dp"
                    android:ellipsize="end"
                    android:gravity="center_vertical"
                    android:maxLines="2"
                    android:textColor="@color/heading_text"
                    app:layout_constraintBottom_toBottomOf="@+id/ivBankLogo"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/ivBankLogo"
                    app:layout_constraintTop_toTopOf="@id/ivBankLogo"
                    tools:text="Bank Amar Indonesia (formerly Anglomas International Bank)" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>