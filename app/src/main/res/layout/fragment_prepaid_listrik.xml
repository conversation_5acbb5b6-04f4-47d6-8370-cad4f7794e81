<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <FrameLayout
            android:id="@+id/fl_top"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/gradient_malibu_to_blue_60"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_16dp"
                android:background="@drawable/bg_rounded_rectangle_white_16dp"
                android:padding="@dimen/_16dp">

                <com.bukuwarung.ui_component.component.inputview.BukuInputView
                    android:id="@+id/biv_customer_id"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:hint="@string/enter_customer_id_hint"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:title="@string/customer_id" />

                <com.bukuwarung.ui_component.component.inputview.BukuInputView
                    android:id="@+id/biv_customer_number"
                    android:layout_width="@dimen/_0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    app:bottomText="@string/listrik_mobile_number"
                    app:hint="@string/enter_phone_number_hint"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/biv_customer_id"
                    app:title="@string/customer_phone" />

                <TextView
                    android:id="@+id/tv_warning"
                    style="@style/Body3"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:background="@drawable/bg_yellow_outline"
                    android:drawablePadding="@dimen/_10dp"
                    android:padding="@dimen/_16dp"
                    android:maxLines="2"
                    android:text="@string/listrik_buy_timings_message"
                    android:textColor="@color/black_80"
                    app:drawableStartCompat="@drawable/ic_warning_yellow"
                    app:layout_constraintTop_toBottomOf="@+id/biv_customer_number" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </FrameLayout>

        <View
            android:id="@+id/view_grey"
            android:layout_width="@dimen/_0dp"
            android:layout_height="@dimen/_0dp"
            android:background="@color/black_5"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/fl_top" />

        <TextView
            android:id="@+id/tv_list_title"
            style="@style/Heading3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="@dimen/_20dp"
            android:text="@string/pilih_nominal"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/view_grey"
            tools:visibility="visible" />

        <include
            android:id="@+id/include_shimmer"
            layout="@layout/layout_product_shimmer_view"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_0dp"
            android:layout_margin="@dimen/dimen_16dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="@id/view_grey"
            tools:visibility="visible" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_products"
            android:layout_width="@dimen/_0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dimen_16dp"
            android:background="@color/black_5"
            android:visibility="gone"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_list_title"
            tools:visibility="visible" />

        <FrameLayout
            android:id="@+id/fl_recent_and_fav"
            android:layout_width="@dimen/_0dp"
            android:layout_height="600dp"
            android:background="@color/black_5"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/view_grey" />

        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.core.widget.NestedScrollView>