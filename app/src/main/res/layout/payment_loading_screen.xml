<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/payment_tab_gradient_bg">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_close"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="@+id/tv_close"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tv_close"
        app:srcCompat="@drawable/ic_cross_black_80"
        app:tint="@color/white" />

    <TextView
        android:id="@+id/tv_close"
        style="@style/Heading2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_8dp"
        android:layout_marginTop="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:text="@string/close"
        android:textColor="@color/white"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/iv_close"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:id="@+id/sv_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/_32dp"
        android:paddingBottom="@dimen/_16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_close">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_20dp"
            android:background="@drawable/bg_solid_grey_border_8dp"
            android:backgroundTint="@color/white"
            android:padding="@dimen/_16dp">

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/lav_animation"
                android:layout_width="150dp"
                android:layout_height="150dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:lottie_autoPlay="true"
                app:lottie_loop="true"
                app:lottie_rawRes="@raw/hourglass" />

            <TextView
                android:id="@+id/tv_payment_status"
                style="@style/Heading2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:text="@string/payment_out_status_pending_title"
                android:textAlignment="center"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/lav_animation" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                app:layout_constraintTop_toBottomOf="@id/tv_payment_status">

                <View
                    android:id="@+id/vw_shimmer_1"
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:background="@drawable/bg_grey_gradient"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/vw_shimmer_2"
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:layout_marginTop="@dimen/_8dp"
                    android:background="@drawable/bg_grey_gradient"
                    app:layout_constraintTop_toBottomOf="@id/vw_shimmer_1" />

                <View
                    android:id="@+id/vw_shimmer_3"
                    android:layout_width="56dp"
                    android:layout_height="44dp"
                    android:layout_marginTop="@dimen/_24dp"
                    android:background="@drawable/bg_grey_gradient"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/vw_shimmer_2" />

                <View
                    android:id="@+id/vw_shimmer_4"
                    android:layout_width="0dp"
                    android:layout_height="9dp"
                    android:layout_marginStart="@dimen/_12dp"
                    android:background="@drawable/bg_grey_gradient"
                    app:layout_constraintBottom_toTopOf="@id/vw_shimmer_5"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/vw_shimmer_3"
                    app:layout_constraintTop_toTopOf="@id/vw_shimmer_3" />

                <View
                    android:id="@+id/vw_shimmer_5"
                    android:layout_width="0dp"
                    android:layout_height="9dp"
                    android:background="@drawable/bg_grey_gradient"
                    app:layout_constraintBottom_toBottomOf="@id/vw_shimmer_3"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@id/vw_shimmer_4"
                    app:layout_constraintTop_toBottomOf="@id/vw_shimmer_4" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>