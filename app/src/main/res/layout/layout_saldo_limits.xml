<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fl_saldo_limit_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="@dimen/_12dp">

    <TextView
        android:id="@+id/tv_saldo_limit"
        style="@style/Body3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:background="@drawable/bg_corner_8dp_stroke_black10"
        android:gravity="center_vertical"
        android:paddingHorizontal="@dimen/_12dp"
        android:paddingVertical="@dimen/_10dp"
        android:textColor="@color/black_60"
        app:drawableEndCompat="@drawable/ic_outline_info"
        app:drawableTint="@color/black_40"
        tools:text="Limit harian pakai saldo tersisa Rp158.000.000" />

</FrameLayout>