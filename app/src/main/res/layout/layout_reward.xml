<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="@dimen/_12dp"
    android:paddingVertical="@dimen/_8dp">

    <ImageView
        android:id="@+id/iv_saldo_reward"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_saldo_icon" />

    <TextView
        android:id="@+id/tv_title"
        style="@style/SubHeading2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintEnd_toStartOf="@+id/sw_reward"
        app:layout_constraintStart_toEndOf="@+id/iv_saldo_reward"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/tv_subtitle"
        tools:text="Pakai Rp49.000 Saldo Bonus" />

    <TextView
        android:id="@+id/tv_subtitle"
        style="@style/Body3.black60"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_12dp"
        android:layout_marginEnd="@dimen/_16dp"
        app:layout_constraintEnd_toStartOf="@+id/sw_reward"
        app:layout_constraintStart_toEndOf="@+id/iv_saldo_reward"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:text="Total Saldo Bonus Rp60.000" />

    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/sw_reward"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:checked="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:thumbTint="@color/transaction_detail_switch_thumb"
        app:trackTint="@color/transaction_detail_switch_track"
        tools:ignore="UseSwitchCompatOrMaterialXml" />
</androidx.constraintlayout.widget.ConstraintLayout>