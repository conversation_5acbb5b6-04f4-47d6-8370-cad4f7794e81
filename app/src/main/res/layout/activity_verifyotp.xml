<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/coordinatorLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/tb_verify_otp"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/colorPrimary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:theme="@style/ToolbarTheme">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:id="@+id/tv_toolbar_title"
                    style="@style/Heading2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:paddingStart="0dp"
                    android:paddingEnd="16dp"
                    android:text="Kode OTP"
                    android:textColor="@color/white"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iv_help"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginEnd="16dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:visibility="gone"
                    app:srcCompat="@drawable/ic_baseline_help_outline"
                    app:tint="@color/white" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.appcompat.widget.Toolbar>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_help"
            style="@style/SubHeading1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="16dp"
            android:backgroundTint="@color/white"
            android:enabled="true"
            android:gravity="center"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:text="@string/help"
            android:textAllCaps="false"
            android:textColor="@color/black_40"
            android:textSize="14sp"
            android:visibility="gone"
            app:cornerRadius="4dp"
            app:icon="@drawable/ic_baseline_help_outline"
            app:iconTint="@color/black_40"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:strokeColor="@color/black_40"
            app:strokeWidth="1dp"
            tools:visibility="visible" />


        <TextView
            android:id="@+id/txt_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="52dp"
            android:text="@string/verify_your_mobile_number"
            android:textAlignment="center"
            android:textColor="@color/black_80"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tb_verify_otp" />


        <TextView
            android:id="@+id/tv_otp_message"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="28dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="28dp"
            android:textAlignment="center"
            android:textColor="@color/black"
            android:textSize="16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/txt_title"
            tools:text="otp_message_placeholder_test" />


        <include
            android:id="@+id/otp_field"
            layout="@layout/otp_field_layout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_otp_message" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_waiting_otp"
            style="@style/Body1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="@dimen/_4dp"
            android:text="@string/otp_waiting_placeholder"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_timer"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/otp_field" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_timer"
            style="@style/Body1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="@dimen/_4dp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintEnd_toStartOf="@id/btn_retry_request_otp"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@id/tv_waiting_otp"
            app:layout_constraintTop_toBottomOf="@id/otp_field"
            tools:text="00:28" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/btn_retry_request_otp"
            style="@style/Body1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_4dp"
            android:layout_marginTop="24dp"
            android:text="@string/resend"
            android:textColor="@color/blue_60"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@id/tv_timer"
            app:layout_constraintTop_toBottomOf="@id/otp_field"
            tools:visibility="visible" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_error_message"
            style="@style/Body3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/bg_warning_login"
            android:drawablePadding="16dp"
            android:gravity="center_vertical"
            android:paddingVertical="16dp"
            android:paddingStart="8dp"
            android:paddingEnd="8dp"
            android:text="@string/phone_number_length_warning"
            android:visibility="gone"
            app:drawableStartCompat="@drawable/ic_warning_red"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_timer"
            tools:visibility="visible" />


        <TextView
            android:id="@+id/btn_switch_otp_channel"
            style="@style/SubHeading1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:enabled="false"
            android:textColor="@color/black_40"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_wording_helper"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            tools:text="Kirim kode melalui SMS" />

        <TextView
            android:id="@+id/tv_wording_helper"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_8dp"
            android:layout_marginBottom="32dp"
            android:text="@string/or_label"
            android:textColor="@color/black_40"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/btn_skip_login"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@id/btn_switch_otp_channel" />

        <TextView
            android:id="@+id/btn_skip_login"
            style="@style/SubHeading1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:text="@string/skip"
            android:textColor="@color/blue_60"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@id/tv_wording_helper" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/group_skip_login"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="btn_skip_login, tv_wording_helper"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"
        tools:background="@color/colorPrimary"
        tools:visibility="visible" />

    <ProgressBar
        android:id="@+id/pb_verify_otp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
