<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?actionBarSize"
            android:background="@color/colorPrimary"
            app:theme="@style/ToolbarTheme"
            app:title="@string/destination_account" />

    </com.google.android.material.appbar.AppBarLayout>

    <ScrollView
        android:id="@+id/sv_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:paddingBottom="@dimen/_20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="20dp"
                android:paddingTop="20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/txt_label_bank"
                    style="@style/Heading3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:lines="1"
                    android:text="@string/destination_bank"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <FrameLayout
                    android:id="@+id/fl_selected_bank"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:background="@drawable/bg_solid_grey_border_8dp"
                    android:backgroundTint="@color/blue_5"
                    app:layout_constraintTop_toBottomOf="@id/txt_label_bank">

                    <include
                        android:id="@+id/layout_selected_bank"
                        layout="@layout/list_item_bank"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </FrameLayout>

                <TextView
                    android:id="@+id/txt_label_account_number"
                    style="@style/Heading3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:lines="1"
                    android:text="@string/account_number"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/fl_selected_bank" />

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/et_account_number"
                    android:layout_width="0dp"
                    android:layout_height="44dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="8dp"
                    android:background="@drawable/drawable_login_input"
                    android:gravity="center_vertical"
                    android:hint="1234 5689 9"
                    android:imeOptions="actionDone"
                    android:inputType="number"
                    android:maxLines="1"
                    android:paddingHorizontal="12dp"
                    android:textColor="@color/black"
                    android:textColorHint="@color/lightBlack"
                    app:layout_constraintEnd_toStartOf="@id/btn_verify"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/txt_label_account_number"
                    tools:ignore="HardcodedText" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_verify"
                    style="@style/ButtonFill.Yellow"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:enabled="false"
                    android:text="@string/verify"
                    android:textAppearance="@style/SubHeading1"
                    app:cornerRadius="8dp"
                    app:layout_constraintBottom_toBottomOf="@id/et_account_number"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/et_account_number" />

                <ProgressBar
                    android:id="@+id/pb_verify_account"
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:indeterminate="true"
                    android:indeterminateTint="@color/black"
                    android:indeterminateTintMode="src_atop"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/btn_verify"
                    app:layout_constraintEnd_toEndOf="@id/btn_verify"
                    app:layout_constraintStart_toStartOf="@id/btn_verify"
                    app:layout_constraintTop_toTopOf="@id/btn_verify" />

                <TextView
                    android:id="@+id/tv_account_error"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:textColor="@color/red_80"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/btn_verify"
                    tools:text="Rekening tidak ditemukan. Pastikan nomor rekening benar dan coba lagi."
                    tools:visibility="visible" />

                <com.bukuwarung.edc.payments.ui.widgets.BankAccountView
                    android:id="@+id/bank_account_view"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@id/tv_account_error"
                    tools:visibility="visible" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>