<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <include
        android:id="@+id/toolbar"
        layout="@layout/widget_toolbar"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ScrollView
        android:id="@+id/sv_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:paddingBottom="@dimen/_20dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingHorizontal="20dp"
                android:paddingTop="20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/txt_label_bank"
                    style="@style/Heading3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:lines="1"
                    android:text="@string/destination_bank"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <FrameLayout
                    android:id="@+id/fl_selected_bank"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_16dp"
                    android:background="@drawable/bg_solid_grey_border_8dp"
                    android:backgroundTint="@color/blue_5"
                    app:layout_constraintTop_toBottomOf="@id/txt_label_bank">

                    <com.bukuwarung.edc.payments.ui.widgets.BankAccountView
                        android:id="@+id/bank_account_view"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="visible" />

                </FrameLayout>

                <TextView
                    android:id="@+id/txt_label_account_number"
                    style="@style/Heading3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:lines="1"
                    android:text="@string/account_number"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/fl_selected_bank" />

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/et_account_number"
                    android:layout_width="match_parent"
                    android:layout_height="44dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="8dp"
                    android:background="@drawable/drawable_login_input"
                    android:gravity="center_vertical"
                    android:hint="Masukkan nomor rekening"
                    android:imeOptions="actionDone"
                    android:inputType="number"
                    android:maxLines="1"
                    android:maxLength="29"
                    android:paddingHorizontal="12dp"
                    android:textColor="@color/black"
                    android:textColorHint="@color/color_hint"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/txt_label_account_number"
                    tools:ignore="HardcodedText" />

                <TextView
                    android:id="@+id/tv_max_acc_error"
                    style="@style/Body3"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:textColor="@color/red_error"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/et_account_number"
                    tools:text="Top up saldo di atas Rp100.000 gratis biaya admin."
                    tools:visibility="visible" />


                <TextView
                    android:id="@+id/tvAmount"
                    style="@style/Heading3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:text="@string/nominal_transfer"
                    android:textColor="@color/black40"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_max_acc_error" />

                <com.bukuwarung.edc.payments.ui.widgets.CurrencyEditText
                    android:id="@+id/et_amount"
                    style="@style/Heading1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:background="@color/white"
                    android:gravity="end"
                    android:hint="@string/zero_amount"
                    android:imeOptions="actionDone"
                    android:inputType="number"
                    android:maxLength="17"
                    android:paddingTop="@dimen/_6dp"
                    android:paddingBottom="@dimen/_6dp"
                    android:singleLine="true"
                    android:text="@string/currency"
                    app:drawableLeftCompat="@drawable/ic_arrow_blue_bg_top_right"
                    app:layout_constraintTop_toBottomOf="@id/tvAmount" />


                <View
                    android:id="@+id/vw_divider"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginTop="@dimen/_4dp"
                    android:background="@color/black_10"
                    app:layout_constraintTop_toBottomOf="@id/et_amount" />

                <TextView
                    android:id="@+id/tv_amount_limit_error"
                    style="@style/Body3"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_8dp"
                    android:visibility="gone"
                    tools:visibility="visible"
                    android:textColor="@color/black_60"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/vw_divider"
                    tools:text="Top up saldo di atas Rp100.000 gratis biaya admin." />


                <TextView
                    android:id="@+id/tvReferenceCode"
                    style="@style/Heading3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:lines="1"
                    android:text="@string/news_optional"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_amount_limit_error" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tiLReferenceCode"
                    android:layout_width="@dimen/_0dp"
                    android:layout_marginTop="@dimen/_12dp"
                    android:layout_height="wrap_content"
                    app:boxBackgroundMode="none"
                    app:counterEnabled="true"
                    app:counterMaxLength="16"
                    app:hintEnabled="false"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tvReferenceCode">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tietReferenceCode"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:background="@drawable/drawable_login_input"
                        android:gravity="center_vertical|start"
                        android:hint="Masukan berita"
                        android:textColorHint="@color/color_hint"
                        android:imeOptions="actionDone"
                        android:maxLength="16"
                        android:paddingHorizontal="@dimen/_8dp" />
                </com.google.android.material.textfield.TextInputLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnNext"
        style="@style/ButtonFill.Yellow"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:enabled="false"
        android:text="@string/next"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>