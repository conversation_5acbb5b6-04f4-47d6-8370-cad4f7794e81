<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/blue_5"
    android:id="@+id/cl_layout"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <TextView
        android:id="@+id/tv_message"
        style="@style/Body3"
        android:layout_width="@dimen/dimen_0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginEnd="@dimen/_4dp"
        tools:text="Pakai saldo, dapet cashback Rp1.500!"
        android:paddingVertical="@dimen/_8dp"
        android:gravity="center"
        android:drawablePadding="@dimen/_4dp"
        app:layout_constraintEnd_toStartOf="@id/btn_check"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_check"
        style="@style/ButtonFill.Blue"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/_16dp"
        android:padding="@dimen/_0dp"
        android:text="@string/choose"
        android:textAllCaps="false"
        android:textAppearance="@style/SubHeading2"
        android:textSize="@dimen/text_14sp"
        app:cornerRadius="@dimen/_4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:rippleColor="@color/black_40" />
</androidx.constraintlayout.widget.ConstraintLayout>