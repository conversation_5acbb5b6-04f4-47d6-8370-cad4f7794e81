<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_solid_white_corner_8dp_stroke_black10"
    android:paddingHorizontal="@dimen/_16dp"
    android:paddingBottom="@dimen/_12dp">

    <TextView
        android:id="@+id/tv_invoice_proof"
        style="@style/SubHeading1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_12dp"
        android:layout_marginStart="@dimen/_16dp"
        android:text="@string/proof_of_trx"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.bukuwarung.edc.payments.util.OrderInvoice
        android:id="@+id/order_invoice"
        android:background="@color/white"
        android:layout_margin="1dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_invoice_proof" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_share"
        android:layout_width="48dp"
        android:layout_height="40dp"
        android:layout_marginLeft="@dimen/_16dp"
        android:background="@drawable/button_border"
        android:gravity="center"
        android:text=""
        android:visibility="gone"
        android:textAllCaps="false"
        android:layout_marginTop="@dimen/_20dp"
        android:textColor="@color/white"
        app:cornerRadius="8dp"
        app:icon="@drawable/ic_share"
        app:iconGravity="textStart"
        app:iconTint="@color/colorPrimary"
        app:iconTintMode="src_in"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/order_invoice" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_print"
        android:layout_width="@dimen/_0dp"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        android:backgroundTint="@color/colorPrimary"
        android:gravity="center"
        android:padding="@dimen/_8dp"
        android:text="@string/transaction_print"
        android:textAllCaps="false"
        android:textColor="@color/white"
        app:cornerRadius="8dp"
        app:icon="@drawable/ic_printer"
        app:iconGravity="textStart"
        app:iconTint="@color/white"
        app:iconTintMode="src_in"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/btn_share"
        app:layout_constraintTop_toBottomOf="@id/order_invoice" />

    <TextView
        android:id="@+id/tv_auto_print"
        style="@style/Label1"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_4dp"
        android:text="@string/auto_print"
        android:textColor="@color/black_60"
        android:textAlignment="center"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btn_print"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>