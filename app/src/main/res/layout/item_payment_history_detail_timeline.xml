<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white">

    <ImageView
        android:id="@+id/icon_status_pending1"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="@dimen/_16dp"
        android:adjustViewBounds="true"
        android:contentDescription="@null"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_check_circle_60" />

    <View
        android:id="@+id/view_pending_status_timeline"
        android:layout_width="wrap_content"
        android:layout_height="0dp"
        android:background="@drawable/bg_timeline_dashed_line"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@id/icon_status_pending1"
        app:layout_constraintStart_toStartOf="@id/icon_status_pending1"
        app:layout_constraintTop_toBottomOf="@id/icon_status_pending1" />


    <TextView
        android:id="@+id/txt_label_status_pending"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="14dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/icon_status_pending1"
        app:layout_constraintTop_toTopOf="parent"
        tools:text='Pembayaran kedaluwarsa' />

    <TextView
        android:id="@+id/txt_status_pending_date"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:textColor="@color/body_text"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/txt_label_status_pending"
        app:layout_constraintTop_toBottomOf="@id/txt_label_status_pending"
        app:layout_goneMarginBottom="@dimen/_8dp"
        tools:text="27 Maret 2022, 15:19" />

    <TextView
        android:id="@+id/txt_info_pending"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:text="@string/average_time_disbursement_info"
        android:textColor="@color/black_60"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/txt_label_status_pending"
        app:layout_constraintTop_toBottomOf="@id/txt_status_pending_date" />

    <TextView
        android:id="@+id/txt_additional_info"
        style="@style/Body3"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_8dp"
        android:fontFamily="@font/roboto"
        android:textColor="@color/black_40"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/txt_label_status_pending"
        app:layout_constraintTop_toBottomOf="@id/txt_info_pending"
        tools:text="Ada gangguan dari operator. coba sesaat lagi"
        tools:visibility="gone" />

    <TextView
        android:id="@+id/txt_show_receipt"
        style="@style/SubHeading2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_5dp"
        android:background="@drawable/bg_button_outline_primary"
        android:paddingStart="@dimen/_10dp"
        android:paddingTop="@dimen/_5dp"
        android:paddingEnd="@dimen/_10dp"
        android:paddingBottom="@dimen/_5dp"
        android:text="@string/show_receipt"
        android:textColor="@color/blue_60"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/txt_label_status_pending"
        app:layout_constraintTop_toBottomOf="@id/txt_additional_info"
        tools:visibility="visible" />

    <View
        android:id="@+id/vw_bottom_padding"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_8dp"
        app:layout_constraintTop_toBottomOf="@id/txt_show_receipt" />

</androidx.constraintlayout.widget.ConstraintLayout>
