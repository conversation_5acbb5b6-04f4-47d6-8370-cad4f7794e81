<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/colorPrimary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:navigationIcon="@drawable/ic_back"
        app:theme="@style/ToolbarTheme"
        app:title="@string/enter_pin_toolbar_title"
        app:titleMarginStart="@dimen/_16dp"
        app:titleTextAppearance="@style/Heading2"
        app:titleTextColor="@color/white" />

    <ProgressBar
        android:id="@+id/main_loader"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/secureInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/_16dp"
        android:background="@drawable/bg_solid_grey100_corner_4dp_stroke_white"
        android:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <ImageView
            android:id="@+id/secureInfoImage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_16dp"
            android:layout_marginTop="@dimen/_12dp"
            android:layout_marginBottom="@dimen/_12dp"
            android:src="@drawable/vector_secure"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/secureInfoHeading"
            style="@style/SubHeading1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:text="@string/dont_share_pin_title"
            app:layout_constraintBottom_toTopOf="@+id/secureInfoBody"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/secureInfoImage"
            app:layout_constraintTop_toTopOf="@id/secureInfoImage" />

        <TextView
            android:id="@+id/secureInfoBody"
            style="@style/Body3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_12dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:text="@string/dont_share_pin_and_otp_body"
            app:layout_constraintBottom_toBottomOf="@+id/secureInfoImage"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/secureInfoImage"
            app:layout_constraintTop_toBottomOf="@+id/secureInfoHeading" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:height="28dp"
        android:text="@string/enter_your_atm_pin"
        android:textAlignment="center"
        android:textColor="@color/black_80"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/secureInfo" />

    <com.bukuwarung.edc.payments.ui.widgets.PinEditText
        android:id="@+id/pin"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_40dp"
        android:layout_marginTop="@dimen/_30dp"
        android:background="@color/transparent"
        android:textColor="@color/transparent"
        app:layout_constraintTop_toBottomOf="@id/title" />

    <TextView
        android:id="@+id/tvPinError"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_marginStart="@dimen/_16dp"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="@dimen/_16dp"
        android:fontFamily="@font/roboto"
        android:lineSpacingExtra="3dp"
        android:textAlignment="center"
        android:textColor="@color/red_error"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/pin"
        tools:text='@string/pin_not_correct'
        tools:visibility="visible" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnReturnMain"
        style="@style/ButtonFill.Yellow"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:enabled="true"
        android:gravity="center"
        android:paddingVertical="@dimen/_12dp"
        android:text="@string/return_to_main_page"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="visible" />

    <com.bukuwarung.edc.payments.ui.widgets.DynamicNumericKeyboard
        android:id="@+id/dynamicKeyboard"
        android:layout_width="match_parent"
        android:layout_height="264dp"
        android:visibility="gone"
        app:Type="Normal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <LinearLayout
        android:id="@+id/llError"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_16dp"
        android:layout_marginBottom="@dimen/_16dp"
        android:background="@drawable/rounded_border_error_8_radius"
        android:elevation="10dp"
        android:orientation="horizontal"
        android:padding="@dimen/_8dp"
        android:visibility="gone"
        android:weightSum="10"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:visibility="gone">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="@dimen/_24dp"
            android:layout_height="@dimen/_24dp"
            android:layout_weight="1"
            app:srcCompat="@drawable/ic_info_red" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvErrorMessage"
            style="@style/Body2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/_8dp"
            android:layout_marginEnd="@dimen/_16dp"
            android:layout_weight="9"
            android:maxLines="3"
            android:text="@string/account_entered_is_wrong" />

    </LinearLayout>

    <include layout="@layout/layout_overlay" />
</androidx.constraintlayout.widget.ConstraintLayout>