<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/_32dp"
    android:background="@drawable/bg_rounded_rectangle_white_16dp"
    android:paddingBottom="@dimen/_16dp">

    <TextView
        android:id="@+id/tv_heading"
        style="@style/Heading2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_20dp"
        android:layout_marginTop="@dimen/_20dp"
        android:text="@string/feature_under_development"
        app:layout_constraintEnd_toStartOf="@id/iv_cross"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/iv_cross"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_10dp"
        android:layout_marginEnd="@dimen/_10dp"
        android:padding="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:srcCompat="@drawable/ic_cross" />

    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/_16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_heading"
        app:srcCompat="@drawable/ic_coming_soon" />

    <TextView
        android:id="@+id/tv_body"
        style="@style/Body2"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_20dp"
        android:layout_marginTop="@dimen/_16dp"
        android:text="@string/feature_under_development_body"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_logo" />

    <com.bukuwarung.ui_component.component.button.BukuButton
        android:id="@+id/bb_understand"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/_20dp"
        android:layout_marginTop="@dimen/_16dp"
        android:padding="@dimen/_16dp"
        app:buttonText="@string/understand"
        app:buttonType="blue80"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_body" />
</androidx.constraintlayout.widget.ConstraintLayout>