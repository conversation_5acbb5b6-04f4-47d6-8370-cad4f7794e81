<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <FrameLayout
        android:id="@+id/change_business_name_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:id="@+id/cl_profile"
        android:layout_height="match_parent">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/tb_profile"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/colorPrimary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:theme="@style/ToolbarTheme">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:id="@+id/tv_toolbar_title"
                    style="@style/Heading2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:paddingStart="0dp"
                    android:paddingEnd="16dp"
                    android:text="Akun"
                    android:textColor="@color/white"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.appcompat.widget.Toolbar>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_phone_section"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            style="@style/Body1"
            app:fontFamily="@font/roboto_bold"
            android:text= "Nomor Telepon yang Terdaftar"
            android:textColor="@color/black"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tb_profile" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_phone"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            style="@style/Body1"
            android:layout_marginTop="5dp"
            android:text= ""
            android:textColor="@color/black_40"
            app:layout_constraintStart_toStartOf="@id/tv_phone_section"
            app:layout_constraintEnd_toEndOf="@id/tv_phone_section"
            app:layout_constraintTop_toBottomOf="@id/tv_phone_section" />

        <include
            android:id="@+id/vw_divider_login_phone"
            layout="@layout/view_divider"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="10dp"
            app:layout_constraintStart_toStartOf="@id/tv_phone"
            app:layout_constraintEnd_toEndOf="@id/tv_phone"
            app:layout_constraintTop_toBottomOf="@id/tv_phone" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_device_section"
            style="@style/Body1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:text="Nomor Seri Perangkat"
            android:textColor="@color/black"
            app:fontFamily="@font/roboto_bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/vw_divider_login_phone" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_device"
            style="@style/Body1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:text="C38753489"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toEndOf="@id/tv_device_section"
            app:layout_constraintStart_toStartOf="@id/tv_device_section"
            app:layout_constraintTop_toBottomOf="@id/tv_device_section" />

        <include
            android:id="@+id/vw_divider_device"
            layout="@layout/view_divider"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="10dp"
            app:layout_constraintEnd_toEndOf="@id/tv_device"
            app:layout_constraintStart_toStartOf="@id/tv_device"
            app:layout_constraintTop_toBottomOf="@id/tv_device" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_terminal_section"
            style="@style/Body1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:text="ID Terminal"
            android:textColor="@color/black"
            app:fontFamily="@font/roboto_bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/vw_divider_device" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_terminal"
            style="@style/Body1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:text="T73648723"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toEndOf="@id/tv_device_section"
            app:layout_constraintStart_toStartOf="@id/tv_device_section"
            app:layout_constraintTop_toBottomOf="@id/tv_terminal_section" />

        <include
            android:id="@+id/vw_divider_terminal"
            layout="@layout/view_divider"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="10dp"
            app:layout_constraintEnd_toEndOf="@id/tv_device"
            app:layout_constraintStart_toStartOf="@id/tv_device"
            app:layout_constraintTop_toBottomOf="@id/tv_terminal" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_account_name_section"
            style="@style/Body1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:text="@string/nama_toko"
            android:textColor="@color/black"
            app:fontFamily="@font/roboto_bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_terminal" />

        <TextView
            android:id="@+id/tv_ubah_account_name"
            style="@style/Body1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="16dp"
            android:text="Ubah"
            android:textColor="@color/colorPrimary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_account_name_section"
            app:layout_constraintBottom_toBottomOf="@id/tv_account_name"/>

        <ProgressBar
            android:id="@+id/pbLoading"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:visibility="gone"
            android:elevation="40dp"
            android:layout_marginEnd="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tv_account_name_section"
            app:layout_constraintBottom_toBottomOf="@id/tv_account_name"
             />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_account_name"
            style="@style/Body1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginEnd="16dp"
            android:text="Toko Cahaya Abadi"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toStartOf="@id/tv_ubah_account_name"
            app:layout_constraintStart_toStartOf="@id/tv_device_section"
            app:layout_constraintTop_toBottomOf="@id/tv_account_name_section" />

        <include
            android:id="@+id/vw_divider_name"
            layout="@layout/view_divider"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="10dp"
            app:layout_constraintEnd_toEndOf="@id/tv_device"
            app:layout_constraintStart_toStartOf="@id/tv_device"
            app:layout_constraintTop_toBottomOf="@id/tv_account_name" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_address_section"
            style="@style/Body1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:text="@string/alamat_toko"
            android:textColor="@color/black"
            app:fontFamily="@font/roboto_bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/vw_divider_name" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_address"
            style="@style/Body1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:text="Jl. Pahlawan Revolusi No.24, Jakarta Timur"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toEndOf="@id/tv_device_section"
            app:layout_constraintStart_toStartOf="@id/tv_device_section"
            app:layout_constraintTop_toBottomOf="@id/tv_address_section" />

        <include
            android:id="@+id/vw_divider_address"
            layout="@layout/view_divider"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="10dp"
            app:layout_constraintEnd_toEndOf="@id/tv_device"
            app:layout_constraintStart_toStartOf="@id/tv_device"
            app:layout_constraintTop_toBottomOf="@id/tv_address" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>