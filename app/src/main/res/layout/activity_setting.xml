<?xml version="1.0" encoding="utf-8"?>

<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white">

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/containerOrderEdc"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/tb_settings"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/colorPrimary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <TextView
                    android:id="@+id/tv_toolbar_title"
                    style="@style/Heading2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:paddingStart="0dp"
                    android:paddingEnd="16dp"
                    android:text="Pengaturan"
                    android:textColor="@color/colorPrimary"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.appcompat.widget.Toolbar>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_device"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:text="PROFIL ANDA"
            android:textColor="@color/black_40"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tb_settings" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/tv_profile"
            style="@style/Heading3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:paddingVertical="14dp"
            android:text="Akun"
            android:textColor="@color/black"
            app:drawableEndCompat="@drawable/vector_chevron_right"
            app:drawableTint="@color/black"
            app:layout_constraintEnd_toEndOf="@id/tv_device"
            app:layout_constraintStart_toStartOf="@id/tv_device"
            app:layout_constraintTop_toBottomOf="@id/tv_device" />

        <include
            android:id="@+id/vw_divider_profile"
            layout="@layout/view_divider"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="10dp"
            app:layout_constraintEnd_toEndOf="@id/tv_device"
            app:layout_constraintStart_toStartOf="@id/tv_device"
            app:layout_constraintTop_toBottomOf="@id/tv_profile" />


        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/tvCashWithdrawalAccount"
            style="@style/Heading3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:paddingVertical="14dp"
            android:text="@string/cash_withdraw_account"
            android:textColor="@color/black"
            app:drawableEndCompat="@drawable/vector_chevron_right"
            app:drawableTint="@color/black"
            app:layout_constraintEnd_toEndOf="@id/tv_device"
            app:layout_constraintStart_toStartOf="@id/tv_device"
            app:layout_constraintTop_toBottomOf="@id/vw_divider_profile" />

        <include
            android:id="@+id/vwCashWithdrawalAccount"
            layout="@layout/view_divider"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="10dp"
            app:layout_constraintEnd_toEndOf="@id/tv_device"
            app:layout_constraintStart_toStartOf="@id/tv_device"
            app:layout_constraintTop_toBottomOf="@id/tvCashWithdrawalAccount" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_payment_section"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="PERANGKAT"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toEndOf="@id/tv_device"
            app:layout_constraintStart_toStartOf="@id/tv_device"
            app:layout_constraintTop_toBottomOf="@id/vwCashWithdrawalAccount" />

        <include
            android:id="@+id/tv_miniedc"
            layout="@layout/item_device_setting"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="@id/tv_device"
            app:layout_constraintStart_toStartOf="@id/tv_device"
            app:layout_constraintTop_toBottomOf="@id/tv_payment_section" />

        <include
            android:id="@+id/tv_printer"
            layout="@layout/item_device_setting"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="@id/tv_device"
            app:layout_constraintStart_toStartOf="@id/tv_device"
            app:layout_constraintTop_toBottomOf="@id/tv_miniedc" />

        <include
            android:id="@+id/tvActivate"
            layout="@layout/item_device_setting"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="@id/tv_device"
            app:layout_constraintStart_toStartOf="@id/tv_device"
            app:layout_constraintTop_toBottomOf="@id/tv_printer" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/tv_test_printer"
            style="@style/Heading3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:paddingVertical="14dp"
            android:text="Tes Cetak Struk"
            android:textColor="@color/black"
            android:visibility="gone"
            app:drawableEndCompat="@drawable/vector_chevron_right"
            app:drawableTint="@color/black"
            app:layout_constraintEnd_toEndOf="@id/tv_device"
            app:layout_constraintStart_toStartOf="@id/tv_device"
            app:layout_constraintTop_toBottomOf="@id/tvActivate" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/tv_payment_admin_fee"
            style="@style/Heading3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:paddingVertical="14dp"
            android:text="Atur Biaya Admin"
            android:textColor="@color/black"
            android:visibility="gone"
            app:drawableEndCompat="@drawable/vector_chevron_right"
            app:drawableTint="@color/black"
            app:layout_constraintEnd_toEndOf="@id/tv_device"
            app:layout_constraintStart_toStartOf="@id/tv_device"
            app:layout_constraintTop_toBottomOf="@id/tv_test_printer" />

        <include
            android:id="@+id/vw_divider_payment"
            layout="@layout/view_divider"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="10dp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="@id/tv_device"
            app:layout_constraintStart_toStartOf="@id/tv_device"
            app:layout_constraintTop_toBottomOf="@id/tv_payment_admin_fee" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvAccessibilitySection"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="@string/accessibility_settings"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toEndOf="@id/tv_device"
            app:layout_constraintStart_toStartOf="@id/tv_device"
            app:layout_constraintTop_toBottomOf="@id/vw_divider_payment" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/tvSound"
            style="@style/Heading3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:paddingVertical="14dp"
            android:text="@string/set_sound_effect"
            android:textColor="@color/black"
            app:drawableEndCompat="@drawable/vector_chevron_right"
            app:drawableTint="@color/black"
            app:layout_constraintEnd_toEndOf="@id/tv_device"
            app:layout_constraintStart_toStartOf="@id/tv_device"
            app:layout_constraintTop_toBottomOf="@id/tvAccessibilitySection" />

        <View
            android:id="@+id/vwDividerAccessibility"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginHorizontal="@dimen/_16dp"
            android:layout_marginTop="10dp"
            android:background="@color/grey_217"
            app:layout_constraintTop_toBottomOf="@id/tvSound" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_change_pin_section"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="PENGATURAN AKUN"
            android:textColor="@color/black_40"
            app:layout_constraintEnd_toEndOf="@id/tv_device"
            app:layout_constraintStart_toStartOf="@id/tv_device"
            app:layout_constraintTop_toBottomOf="@id/vwDividerAccessibility" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/tv_change_pin"
            style="@style/Heading3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:paddingVertical="14dp"
            android:text="Ubah PIN Saldo"
            android:textColor="@color/black"
            app:drawableEndCompat="@drawable/vector_chevron_right"
            app:drawableTint="@color/black"
            app:layout_constraintEnd_toEndOf="@id/tv_device"
            app:layout_constraintStart_toStartOf="@id/tv_device"
            app:layout_constraintTop_toBottomOf="@id/tv_change_pin_section" />

        <include
            android:id="@+id/vw_divider_change_pin"
            layout="@layout/view_divider"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="10dp"
            app:layout_constraintEnd_toEndOf="@id/tv_device"
            app:layout_constraintStart_toStartOf="@id/tv_device"
            app:layout_constraintTop_toBottomOf="@id/tv_change_pin" />

        <androidx.constraintlayout.widget.Group
        android:id="@+id/grp_pin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tv_change_pin_section, tv_change_pin, vw_divider_change_pin" />

    <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_logout"
            style="@style/Heading3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="Keluar"
            android:textColor="@color/red_error"
            app:layout_constraintEnd_toEndOf="@id/tv_device"
            app:layout_constraintStart_toStartOf="@id/tv_device"
            app:layout_constraintTop_toBottomOf="@id/vw_divider_change_pin" />

        <include
            android:id="@+id/vw_logout"
            layout="@layout/view_divider"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginTop="10dp"
            app:layout_constraintEnd_toEndOf="@id/tv_device"
            app:layout_constraintStart_toStartOf="@id/tv_device"
            app:layout_constraintTop_toBottomOf="@id/tv_logout" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_app_version_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            app:layout_constraintBottom_toTopOf="@id/tv_app_version_code"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_app_version_code"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:textColor="@color/black"
            app:layout_constraintBottom_toTopOf="@+id/tv_phone_number"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_phone_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:textColor="@color/black"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>

