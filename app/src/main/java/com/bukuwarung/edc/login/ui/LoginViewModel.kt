package com.bukuwarung.edc.login.ui

import Resource
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.edc.global.enums.AuthActions
import com.bukuwarung.edc.global.enums.CommunicationChannel
import com.bukuwarung.edc.global.network.interceptors.NoConnectivityException
import com.bukuwarung.edc.login.data.model.BookEntity
import com.bukuwarung.edc.login.data.model.Business
import com.bukuwarung.edc.login.data.model.LoginRequest
import com.bukuwarung.edc.login.data.model.LoginResponse
import com.bukuwarung.edc.login.usecase.EnablePaymentUseCase
import com.bukuwarung.edc.login.usecase.LoginUseCase
import com.bukuwarung.edc.session.SessionManager
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.sharedPreferences
import com.bukuwarung.edc.util.put
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.firestore.FirebaseFirestore
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import org.json.JSONObject
import java.util.*
import javax.inject.Inject

@HiltViewModel
class LoginViewModel @Inject constructor(private val loginUseCase: LoginUseCase): ViewModel() {

    private val _login = MutableLiveData<Resource<LoginResponse>>()

    val login: LiveData<Resource<LoginResponse>>
        get() = _login

    fun requestOtp(phoneNumber: String, otpChannel: CommunicationChannel, action: AuthActions)  = viewModelScope.launch {
        val loginRequest = LoginRequest(
            countryCode = if(otpChannel == CommunicationChannel.SMS) "+62" else "62",
            method = otpChannel.channel,
            action = action?.action
        )
        performUserOnboardingRequirementCheck(phoneNumber);
        _login.postValue(Resource.loading(null))
        try {
            loginUseCase.fetchOtp(loginRequest, phoneNumber).let { it ->
                if (it.isSuccessful) {
                    it.body()?.token?.let { _token ->
                        if (action == AuthActions.SELLER_OTP_REQUEST)
                            sharedPreferences.put("seller_op_token", _token)
                        else
                            sharedPreferences.put("op_token", _token)
                        _login.postValue(Resource.success(it.body()))
                    }
                } else {
                    val json = JSONObject(it.errorBody()?.string())

                    var message = ""
                    if (json.has("message")) {
                        message = json.getString("message")
                    }
                    _login.postValue(Resource.error(message, null))
                }
            }
        } catch (e: NoConnectivityException){
            _login.postValue(Resource.noInternet(null))
        } catch (e: Exception) {
            _login.postValue(Resource.error("-1", null))
            FirebaseCrashlytics.getInstance().recordException(e)
        }
    }

    private fun performUserOnboardingRequirementCheck(phone: String) {
        try{
        if ( (phone.isNullOrEmpty() || phone == SessionManager.getInstance().userIdForExistingBusinessCheck
                    ) && SessionManager.getInstance().hasExistingBusiness())
        {
            return
        }

            SessionManager.getInstance().setExistingBusinessFlag(false)
            SessionManager.getInstance().userIdForExistingBusinessCheck = ""
            FirebaseFirestore
                .getInstance().collection("book_store")
                .whereEqualTo("ownerId", phone)
                .get()
                .addOnCompleteListener { task ->
                    if (task.isSuccessful) {
                        val documents = task.result.documents
                        var hasMatchingDevice = false

                        for (document in documents) {
                            try {
                                val createdByDevice = document.getString("createdByDevice")
                                val updatedByDevice = document.getString("updatedByDevice")
                                if (updatedByDevice == "bukuagen-edc-order" || createdByDevice == "bukuagen-edc-order") {
                                    hasMatchingDevice = true
                                    SessionManager.getInstance().setPaymentAccountIdForExistingBusinessCheck(document.getString("bookId"))
                                    break
                                }
                            }catch (e:Exception){
                                e.printStackTrace();
                            }
                        }

                        // Set the flag if any document matched the createdByDevice condition
                        SessionManager.getInstance().setExistingBusinessFlag(hasMatchingDevice)
                        if (hasMatchingDevice) {
                            SessionManager.getInstance().userIdForExistingBusinessCheck = phone
                        }
                    }
                }
        }catch (e:Exception){
            e.printStackTrace()
        }
    }

}