package com.bukuwarung.edc.homepage.ui.ticker

import android.os.Build
import android.os.Bundle
import android.text.method.LinkMovementMethod
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.edc.R
import com.bukuwarung.edc.card.domain.model.consts.Constants.DEVICE_MANUFACTURER_MOREFUN
import com.bukuwarung.edc.card.domain.model.consts.Constants.DEVICE_MANUFACTURER_PAX
import com.bukuwarung.edc.card.domain.model.consts.Constants.DEVICE_MANUFACTURER_VERIFONE
import com.bukuwarung.edc.databinding.FragmentHomeTickerBinding
import com.bukuwarung.edc.homepage.constant.HomePageRemoteConfig
import com.bukuwarung.edc.homepage.data.model.TickerFragmentData
import com.bukuwarung.edc.util.DateTimeUtils
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.Utils.bwLog
import com.bukuwarung.edc.util.isNotNullOrEmpty
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.textHTML
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import dagger.hilt.android.AndroidEntryPoint
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.TimeZone
import java.util.regex.Pattern

@AndroidEntryPoint
class HomeTickerFragment: Fragment() {

    private lateinit var binding: FragmentHomeTickerBinding

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        binding = FragmentHomeTickerBinding.inflate(layoutInflater, container, false)
        try{
            val tickerDataBlock = HomePageRemoteConfig.getTickerFragmentData()
            val type = object : TypeToken<TickerFragmentData>() {}.type
            val gson: Gson = GsonBuilder().create()

            val tickerData: TickerFragmentData = gson.fromJson(tickerDataBlock, type)

            val hours: Double = 24/tickerData.frequency.orNil

            val tickerDataInterval: Long = hours.times(60).times(60).times(1000).toLong()


            if ((System.currentTimeMillis() - Utils.getTickerCloseTime() > tickerDataInterval) && System.currentTimeMillis() > getTime(tickerData.startTime) && System.currentTimeMillis() < getTime(tickerData.endTime)) {
                binding.tvTickerHeader.apply {
                    setOnTouchListener { view, motionEvent ->
                        if (motionEvent.action == MotionEvent.ACTION_UP) {
                            if (motionEvent.rawX >= right - totalPaddingRight) {
                                Utils.setTickerCloseTime()
                                removeFragment()
                            }
                            true
                        }
                        true;
                    }
                }

                val header = tickerData.tickerHeader
                val bodyContent = tickerData.tickerBody

                if (header.isNullOrEmpty()) {
                    removeFragment()
                }

                binding.apply {
                    tvTickerHeader.textHTML(header)
                    tvTickerBody.textHTML(bodyContent)

                    tvTickerBody.setOnClickListener {
                        try {
                            val link: String? = Utils.extractLinkFromHtml(bodyContent.orEmpty())
                            if (link.isNotNullOrEmpty() && (Build.MANUFACTURER == DEVICE_MANUFACTURER_PAX || Build.MANUFACTURER == DEVICE_MANUFACTURER_VERIFONE || Build.MANUFACTURER == DEVICE_MANUFACTURER_MOREFUN)) {
                                requireActivity().openActivity(WebviewActivity::class.java) {
                                    putString(
                                        ClassConstants.WEBVIEW_TITLE,
                                        getString(R.string.help)
                                    )
                                    putString(
                                        ClassConstants.WEBVIEW_URL,
                                        link
                                    )
                                }
                            } else {
                                tvTickerBody.movementMethod = LinkMovementMethod.getInstance()
                            }
                        }catch (e:Exception){
                            bwLog(e = e)
                        }
                    }
                }
            } else {
                removeFragment()
            }
        }catch (e:Exception){
            bwLog("Exception in HomeTickerFragment",e)
            removeFragment()
            e.printStackTrace()
        }
        return binding.root
    }

    private fun removeFragment() {
        if (isAdded && !isDetached && activity != null && !requireActivity().isFinishing) {
            val manager = requireActivity().supportFragmentManager
            manager.beginTransaction().remove(this@HomeTickerFragment).commit()
        }
    }

    private fun getTime(date: String?) : Long {
        if (date == null) {
            return 0
        }
        val utcFormat = SimpleDateFormat(DateTimeUtils.YYYY_MM_DD_T_HH_MM_SS, Locale.getDefault())
        utcFormat.timeZone = TimeZone.getTimeZone("UTC")

        return utcFormat.parse(date).time
    }



}