package com.bukuwarung.edc.homepage.data.datasource

import com.bukuwarung.edc.card.ui.edcdevices.model.DeviceListResponse
import com.bukuwarung.edc.homepage.data.model.LogonDataRequest
import com.bukuwarung.edc.homepage.data.model.LeaderboardWhitelistedResponse
import com.bukuwarung.edc.login.data.model.LogonResponse
import com.bukuwarung.edc.login.data.model.UserDetail
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path
import retrofit2.http.Query

interface HomePageDataSource {

    @GET("/ac/api/v2/edc/device-detail")
    suspend fun fetchUserDetails(
        @Query("serial_number") serialNumber: String? = null
    ): Response<UserDetail>

    @POST("/edc-adapter/network/{accountId}/collect/token")
    suspend fun fetchLogonData(@Path("accountId") accountId: String, @Body logonDataRequest: LogonDataRequest): Response<LogonResponse>

    @GET("loyalty/api/v1/leaderboard/edc")
    suspend fun userWhitelistedForLeaderboard(
        @Query("campaign") campaign: String,
        @Query("check_whitelist") checkWhitelist: Boolean
    ): Response<LeaderboardWhitelistedResponse>

    @GET("ac/api/v2/edc/list-devices")
    suspend fun getDeviceList(@Query("edc_plan") edcPlan: String): Response<DeviceListResponse>

}