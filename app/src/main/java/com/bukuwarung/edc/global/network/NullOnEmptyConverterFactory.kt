package com.bukuwarung.edc.global.network

import okhttp3.ResponseBody
import retrofit2.Converter
import retrofit2.Retrofit
import java.lang.reflect.Type


class NullOnEmptyConverterFactory : Converter.Factory() {
    override fun responseBodyConverter(type: Type, annotations: Array<out Annotation>, retrofit: Retrofit): Converter<ResponseBody, *>? {
        val converter: Converter<ResponseBody, Any> =
                retrofit.nextResponseBodyConverter(this, type, annotations)

        return Converter { body: ResponseBody ->
            if (body.contentLength() == 0L) {
                null
            } else {
                converter.convert(body)
            }
        }
    }

}
