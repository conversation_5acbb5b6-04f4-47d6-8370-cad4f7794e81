package com.bukuwarung.edc.global.network.interceptors

import android.os.Build
import androidx.annotation.NonNull
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.settings.ui.setting.MiniatmDebugSettingActivity
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.orDefault
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException
import java.util.*

class HeadersInterceptor : Interceptor {

    @NonNull
    @Throws(IOException::class)
    override fun intercept(@NonNull chain: Interceptor.Chain): Response {
        val deviceBrand = Utils.getDeviceBrand()
        val deviceModal = Utils.getDeviceModel()
        val newRequest = chain.request().newBuilder()
            .addHeader("Content-Type", "application/json")
            .addHeader("Accept", "application/json")
            .addHeader("USER-PSEUDO-INSTANCE-ID", "edc-instance")
            .addHeader("X-APP-VERSION-NAME", "3.74.0")
            .addHeader("testing-mock", Utils.getBooleanConfig(MiniatmDebugSettingActivity.TEST_MOCK_API).toString())
            .addHeader("buku-origin", "bukuwarung-edc")
            .addHeader("X-APP-VERSION-CODE", "5000")
            .addHeader("x-device-brand", deviceBrand.orDefault(Build.BRAND))
            .addHeader("x-device-model", deviceModal.orDefault(Build.MODEL))
            .addHeader("X-TIMEZONE", TimeZone.getDefault().id)
            .addHeader("x-edc-app-version-name", BuildConfig.VERSION_NAME)
            .addHeader("x-edc-app-version-code", "${BuildConfig.VERSION_CODE}")
            .build()
        return chain.proceed(newRequest)
    }
}