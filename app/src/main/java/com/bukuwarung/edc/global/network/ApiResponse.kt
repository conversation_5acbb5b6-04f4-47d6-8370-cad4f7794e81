package com.bukuwarung.edc.global.network

import android.util.Log
import androidx.annotation.Keep
import com.bukuwarung.edc.global.Constant
import okhttp3.Headers
import org.json.JSONObject
import retrofit2.Response
import java.net.UnknownHostException
import java.util.regex.Pattern

@Suppress("unused") // T is used in extending classes
sealed class ApiResponse<T> {
    companion object {
        fun <T> create(error: Throwable): ApiErrorResponse<T> {
            val message = if (error.message.isNullOrBlank() || error is UnknownHostException) {
                Constant.NO_INTERNET_ERROR_MESSAGE //TODO: should be able to put this to strings.xml
            } else {
                error.message
            }
            return ApiErrorResponse(message ?: "<PERSON><PERSON><PERSON><PERSON>")
        }

        fun <T> create(response: Response<T>): ApiResponse<T> {
            response.headers()
            return if (response.isSuccessful) {
                val body = response.body()
                if (body == null || response.code() == 204) {
                    ApiEmptyResponse()
                } else {
                    ApiSuccessResponse(
                        body = body,
                        linkHeader = response.headers().get("link"),
                        headers = response.headers()
                    )
                }
            } else {
                val msg = response.errorBody()?.string()
                val errorMsg = if (msg.isNullOrEmpty()) {
                    response.message()
                } else {
                    getErrorMessage(msg)
                }
                val code = getErrorCode(msg ?: "")
                val errorTypeCode = getErrorTypeCode(msg ?: "")
                ApiErrorResponse(errorMsg, response.code(), code,errorTypeCode)
            }
        }

        private fun getErrorMessage(response: String): String {
            if (response.isBlank()) return response
            return try {
                val json = JSONObject(response)

                when {
                    json.has("message") -> json.getString("message")
                    json.has("errors") -> json.getString("errors")
                    else -> response
                }
            } catch (e: Exception) {
                response
            }
        }

        private fun getErrorCode(response: String): String {
            if (response.isBlank()) return response
            return try {
                val json = JSONObject(response)

                when {
                    json.has("code") -> json.getString("code")
                    else -> response
                }
            } catch (e: Exception) {
                response
            }
        }
        private fun getErrorTypeCode(response: String): Int {
            if (response.isBlank()) return -1
            return try {
                val json = JSONObject(response)

                when {
                    json.has("errorCode") -> json.getInt("errorCode")
                    else -> -1
                }
            } catch (e: Exception) {
                -1
            }
        }
    }
}

/**
 * separate class for HTTP 204 responses so that we can make ApiSuccessResponse's body non-null.
 */
class ApiEmptyResponse<T> : ApiResponse<T>()

data class ApiSuccessResponse<T>(
    val body: T,
    val links: Map<String, String>,
    val headers: Headers?
) : ApiResponse<T>() {
    constructor(body: T, linkHeader: String?, headers: Headers?) : this(
        body = body,
        links = linkHeader?.extractLinks() ?: emptyMap(),
        headers = headers
    )

    val nextPage: Int? by lazy(LazyThreadSafetyMode.NONE) {
        links[NEXT_LINK]?.let { next ->
            val matcher = PAGE_PATTERN.matcher(next)
            if (!matcher.find() || matcher.groupCount() != 1) {
                null
            } else {
                try {
                    Integer.parseInt(matcher.group(1)!!)
                } catch (ex: NumberFormatException) {
                    Log.e("ApiResponse","cannot parse next page from $next")
                    null
                }
            }
        }
    }

    companion object {
        private val LINK_PATTERN = Pattern.compile("<([^>]*)>[\\s]*;[\\s]*rel=\"([a-zA-Z0-9]+)\"")
        private val PAGE_PATTERN = Pattern.compile("\\bpage=(\\d+)")
        private const val NEXT_LINK = "next"

        private fun String.extractLinks(): Map<String, String> {
            val links = mutableMapOf<String, String>()
            val matcher = LINK_PATTERN.matcher(this)

            while (matcher.find()) {
                val count = matcher.groupCount()
                if (count == 2) {
                    links[matcher.group(2)!!] = matcher.group(1)!!
                }
            }
            return links
        }

    }
}

data class ApiErrorResponse<T>(val errorMessage: String, val statusCode: Int = -1, val code: String = "",val errorTypeCode : Int = -1) : ApiResponse<T>()

@Keep
data class ResponseWrapper<T>(
    val result : Boolean,
    val data : T
)
