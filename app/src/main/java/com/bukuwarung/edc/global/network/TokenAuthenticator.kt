package com.bukuwarung.edc.global.network

import android.os.Build
import android.util.Log
import com.bukuwarung.analytic.BtAnalytics.bwLog
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.network.model.request.SessionRequest
import com.bukuwarung.edc.global.network.session.SessionRemoteDataSource
import com.bukuwarung.edc.global.network.session.SessionRepository
import com.bukuwarung.edc.util.Utils.sharedPreferences
import com.bukuwarung.edc.util.get
import com.bukuwarung.edc.util.put
import com.bukuwarung.edc.util.EncryptedPreferencesHelper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import okhttp3.Authenticator
import okhttp3.Request
import okhttp3.Response
import okhttp3.Route

class TokenAuthenticator : Authenticator {

    private val maxRefreshRetries = 3

    @Synchronized
    override fun authenticate(route: Route?, response: Response): Request? {
        return runBlocking(Dispatchers.IO) { authentication(route, response) }
    }

    private suspend fun authentication(route: Route?, response: Response): Request? {
        try {
            if (responseCount(response) > maxRefreshRetries){
                return null
            }
            val url = response.request.url.toUri().toString()
            // hack for auth OTP verify doing too many follow-up calls
            if (url.contains("api/v1/auth/otp/verify")){
                return null
            }

            if (sharedPreferences.get("token_refresh_fail", false)) {
                sharedPreferences.put("token_refresh_fail", false)
                return null
            }

            if (System.currentTimeMillis() -
                sharedPreferences.get("session_start", 0L) < Constant.SESSION_TIME
            ) {
                val token = EncryptedPreferencesHelper.get("bukuwarung_token", "")
                if (token.isEmpty()) return null
                logMessage("Returned with updated request")
                return createNewRequest(response, token)
            }
            bwLog("TokenAuthenticator from $url")
            val sessionRepository = SessionRepository(
                NetworkModule.retrofit(
                    Constant.BASE_URL
                ).create(SessionRemoteDataSource::class.java)
            )

            val token: String? = sharedPreferences.getString("session_token", null)
            if (token.isNullOrEmpty()) {
                return null
            }

            val newSessionRequest = SessionRequest(
                token = token,
                register = false,
                deviceId = "",
                deviceModel = Build.MODEL,
                deviceBrand = Build.MANUFACTURER
            )

            val apiResponse = sessionRepository.createNewSession(newSessionRequest)

            if (apiResponse.isSuccessful) {
                val newSession = apiResponse.body()
                EncryptedPreferencesHelper.put("bukuwarung_token", newSession?.idToken)
                sharedPreferences.put("session_token", newSession?.refreshToken)
                sharedPreferences.put("session_start", System.currentTimeMillis())
                sharedPreferences.put("token_refresh_fail", false)

                logMessage("Return with updated request")
                return newSession?.idToken?.let { createNewRequest(response, it) }
            } else {
                sharedPreferences.put("token_refresh_fail", true)
                bwLog(Exception("token_refresh_fail $apiResponse"))
                return null
            }
        } catch (e: Exception) {
            sharedPreferences.put("token_refresh_fail", true)
            bwLog(e)
            return null
        }
    }

    private fun responseCount(response: Response): Int {
        var result = 0
        var priorResponse = response.priorResponse

        while (priorResponse != null) {
            result++
            priorResponse = priorResponse.priorResponse
            if (result > maxRefreshRetries) {
                result = Int.MAX_VALUE
                break
            }
        }
        return result
    }

    private fun createNewRequest(response: Response, token: String) =
        response.request.newBuilder()
            .header("Authorization", "Bearer $token")
            .build()

    private fun logMessage(message: String) {
        if (BuildConfig.DEBUG)
            Log.d("OkHttpClient: ", "Retrofit Authenticator ->$message")
    }
}