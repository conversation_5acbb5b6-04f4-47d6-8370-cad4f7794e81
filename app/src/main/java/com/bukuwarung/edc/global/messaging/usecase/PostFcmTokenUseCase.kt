package com.bukuwarung.edc.global.messaging.usecase

import com.bukuwarung.edc.global.Constant.clientID
import com.bukuwarung.edc.global.Constant.clientSecret
import com.bukuwarung.edc.global.messaging.data.model.FcmTokenRequest
import com.bukuwarung.edc.global.messaging.data.repository.FcmRepository
import com.bukuwarung.edc.global.network.ApiEmptyResponse
import com.bukuwarung.edc.global.network.ApiSuccessResponse
import com.bukuwarung.edc.util.EncryptedPreferencesHelper
import com.bukuwarung.edc.util.Utils
import com.google.firebase.messaging.FirebaseMessaging
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.tasks.await

class PostFcmTokenUseCase(private val repository: FcmRepository) {
    private val mutex = Mutex()
    private var storingToken = ""

    suspend fun invoke() {
        if (Utils.isPaxVerifoneDevice()) {
            setShouldRetryPostFcmToken(false)
            return
        }
        mutex.withLock {
            val newFcmToken: String? = try {
                FirebaseMessaging.getInstance().token.await()
            } catch (ex: Exception) {
                setShouldRetryPostFcmToken(true)
                return
            }
            val deviceId = Utils.getFcmDeviceId()
            val isLoggedIn = EncryptedPreferencesHelper.get("uuid", "").isNotEmpty()
            if (!isLoggedIn || newFcmToken.isNullOrEmpty()) {
                setShouldRetryPostFcmToken(true)
                return
            }
            val storedFcmToken = Utils.getLastStoredFcmToken()
            if (newFcmToken == storedFcmToken || newFcmToken == storingToken) {
                return
            } else {
                storingToken = newFcmToken
            }
            val request = FcmTokenRequest(clientID, clientSecret, newFcmToken, deviceId)
            when (val res = repository.postToken(request)) {
                is ApiEmptyResponse,
                is ApiSuccessResponse -> {
                    setShouldRetryPostFcmToken(false)
                    Utils.setLastStoredFcmToken(newFcmToken)
                }

                else -> {
                    res.toString()
                    setShouldRetryPostFcmToken(true)
                }
            }
        }
    }

    fun invokeIfNeeded() {
        if (Utils.getShouldRetryPostFcmToken()) {
            CoroutineScope(Dispatchers.IO).launch { invoke() }
        }
    }

    private fun setShouldRetryPostFcmToken(value: Boolean) {
        Utils.setShouldRetryPostFcmToken(value)
    }
}
