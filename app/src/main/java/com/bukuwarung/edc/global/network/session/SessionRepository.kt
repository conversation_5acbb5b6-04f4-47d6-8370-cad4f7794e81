package com.bukuwarung.edc.global.network.session

import com.bukuwarung.edc.global.network.model.request.SessionRequest
import com.bukuwarung.edc.global.network.model.response.SessionResponse
import retrofit2.Response
import javax.inject.Inject

class SessionRepository @Inject constructor(private val remoteDataSource: SessionRemoteDataSource) :
    SessionRemoteRepository {
    override suspend fun createNewSession(sessionRequest: SessionRequest) =
        remoteDataSource.createNewSession(sessionRequest)
}

interface SessionRemoteRepository {
    suspend fun createNewSession(sessionRequest: SessionRequest): Response<SessionResponse>
}
