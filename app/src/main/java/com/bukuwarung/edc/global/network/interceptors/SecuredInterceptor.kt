package com.bukuwarung.edc.global.network.interceptors

import androidx.annotation.NonNull
import com.bukuwarung.edc.util.EncryptedPreferencesHelper
import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException

class SecuredInterceptor : Interceptor {

    @NonNull
    @Throws(IOException::class)
    override fun intercept(@NonNull chain: Interceptor.Chain): Response {
        val newRequest = chain.request().newBuilder()
            .addHeader("Authorization", getAuthToken())
            .build()
        return chain.proceed(newRequest)
    }

    private fun getAuthToken() = "Bearer ${EncryptedPreferencesHelper.get("bukuwarung_token", "")}"

    companion object {
        private const val KEY_TOKEN = "key_token"
    }
}