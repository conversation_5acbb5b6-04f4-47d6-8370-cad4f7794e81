package com.bukuwarung.edc.replacement.presentation.dialog

import android.net.Uri
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.MediaController
import androidx.fragment.app.DialogFragment
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.DialogReplacementVideoPlaybackBinding

class ReplacementVideoPlaybackDialog : DialogFragment() {

    private var _binding: DialogVideoPlaybackBinding? = null
    private val binding get() = _binding!!
    
    private var videoPath: String? = null
    private var mediaController: MediaController? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.FullScreenDialogStyle)
        
        arguments?.let {
            videoPath = it.getString(ARG_VIDEO_PATH)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = DialogVideoPlaybackBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupDialog()
        setupVideoPlayer()
    }

    private fun setupDialog() {
        binding.btnClose.setOnClickListener {
            dismiss()
        }

        // Make dialog fill most of the screen width
        dialog?.window?.setLayout(
            (resources.displayMetrics.widthPixels * 0.9).toInt(),
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
    }

    private fun setupVideoPlayer() {
        val path = videoPath ?: return
        
        try {
            val uri = Uri.parse(path)
            
            // Setup media controller with disabled autoplay
            mediaController = MediaController(requireContext()).apply {
                setAnchorView(binding.videoView)
            }
            
            binding.videoView.apply {
                setMediaController(mediaController)
                setVideoURI(uri)

                // Disable autoplay - video won't start automatically
                setOnPreparedListener { mediaPlayer ->
                    binding.pbLoading.visibility = View.GONE
                    binding.ivPlayButton.visibility = View.VISIBLE

                    // Set proper aspect ratio
                    val videoWidth = mediaPlayer.videoWidth
                    val videoHeight = mediaPlayer.videoHeight

                    if (videoWidth > 0 && videoHeight > 0) {
                        val aspectRatio = videoWidth.toFloat() / videoHeight.toFloat()
                        val screenWidth = (resources.displayMetrics.widthPixels * 0.9).toInt()
                        val maxHeight = (resources.displayMetrics.heightPixels * 0.6).toInt()

                        val calculatedHeight = (screenWidth / aspectRatio).toInt()
                        val finalHeight = if (calculatedHeight > maxHeight) maxHeight else calculatedHeight

                        // Update VideoView dimensions
                        val layoutParams = binding.videoView.layoutParams
                        layoutParams.width = screenWidth - (resources.displayMetrics.density * 32).toInt() // Account for margins
                        layoutParams.height = finalHeight
                        binding.videoView.layoutParams = layoutParams
                    }

                    // Ensure autoplay is disabled
                    mediaPlayer.seekTo(0)
                }

                setOnCompletionListener {
                    binding.ivPlayButton.visibility = View.VISIBLE
                }

                setOnErrorListener { _, _, _ ->
                    binding.pbLoading.visibility = View.GONE
                    binding.ivPlayButton.visibility = View.VISIBLE
                    true
                }
            }
            
            // Setup play button click
            binding.ivPlayButton.setOnClickListener {
                if (binding.videoView.isPlaying) {
                    binding.videoView.pause()
                    binding.ivPlayButton.visibility = View.VISIBLE
                } else {
                    binding.videoView.start()
                    binding.ivPlayButton.visibility = View.GONE
                }
            }
            
            // Show loading initially
            binding.pbLoading.visibility = View.VISIBLE
            binding.ivPlayButton.visibility = View.GONE
            
        } catch (e: Exception) {
            binding.pbLoading.visibility = View.GONE
            binding.ivPlayButton.visibility = View.VISIBLE
        }
    }

    override fun onPause() {
        super.onPause()
        if (binding.videoView.isPlaying) {
            binding.videoView.pause()
            binding.ivPlayButton.visibility = View.VISIBLE
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        binding.videoView.stopPlayback()
        mediaController = null
        _binding = null
    }

    companion object {
        private const val ARG_VIDEO_PATH = "video_path"
        
        fun newInstance(videoPath: String): ReplacementVideoPlaybackDialog {
            return ReplacementVideoPlaybackDialog().apply {
                arguments = Bundle().apply {
                    putString(ARG_VIDEO_PATH, videoPath)
                }
            }
        }
    }
}
