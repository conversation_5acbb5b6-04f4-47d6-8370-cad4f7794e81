package com.bukuwarung.edc.replacement.domain.usecase

import com.bukuwarung.edc.replacement.data.mapper.toDomain
import com.bukuwarung.edc.replacement.domain.ReplacementRepository
import com.bukuwarung.edc.replacement.domain.model.ReplacementFormReason
import com.bukuwarung.edc.replacement.presentation.form.ReplacementDeviceType
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class GetReplacementReasonsUseCase @Inject constructor(
    private val repository: ReplacementRepository
) {
    suspend operator fun invoke(deviceType: ReplacementDeviceType): Result<List<ReplacementFormReason>> {
        return try {
            val response = repository.getReplacementReasons(deviceType.value)
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body?.result == true) {
                    Result.success(body.data.toDomain())
                } else {
                    Result.failure(Exception("API returned result: false"))
                }
            } else {
                Result.failure(Exception("HTTP ${response.code()}: ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
