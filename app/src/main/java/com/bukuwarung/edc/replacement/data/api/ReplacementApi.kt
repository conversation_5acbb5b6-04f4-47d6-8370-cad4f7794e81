package com.bukuwarung.edc.replacement.data.api

import com.bukuwarung.edc.replacement.data.model.ReplacementEvidenceResponse
import com.bukuwarung.edc.replacement.data.model.ReplacementReasonResponse
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part
import retrofit2.http.Query

interface ReplacementApi {
    
    @GET("ac/api/v2/edc/replacement/reasons")
    suspend fun getReplacementReasons(@Query("deviceType") deviceType: String): Response<ReplacementReasonResponse>
    
    @Multipart
    @POST("ac/api/v2/upload/replacement-evidence")
    suspend fun uploadReplacementEvidence(
        @Part("category") category: RequestBody,
        @Part("fileName") fileName: RequestBody,
        @Part file: MultipartBody.Part
    ): Response<ReplacementEvidenceResponse>
}
