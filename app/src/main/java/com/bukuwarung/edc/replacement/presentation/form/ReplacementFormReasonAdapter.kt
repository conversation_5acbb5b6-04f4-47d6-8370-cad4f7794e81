package com.bukuwarung.edc.replacement.presentation.form

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bukuwarung.edc.databinding.ReplacementItemReasonBinding
import com.bukuwarung.edc.replacement.domain.model.ReplacementFormReason

class ReplacementFormReasonAdapter(
    private val callback: Callback
) : RecyclerView.Adapter<ReplacementFormReasonAdapter.ReasonViewHolder>() {

    private var data = emptyList<ReplacementFormReason>()

    fun submitList(newList: List<ReplacementFormReason>) {
        data = newList
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ReasonViewHolder {
        val itemBinding =
            ReplacementItemReasonBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ReasonViewHolder(itemBinding)
    }

    override fun getItemCount(): Int = data.size

    override fun onBindViewHolder(holder: ReasonViewHolder, position: Int) {
        holder.bind(data[position], position)
    }

    inner class ReasonViewHolder(
        private val binding: ReplacementItemReasonBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(item: ReplacementFormReason, itemPosition: Int) {
            binding.cbReason.apply {
                setOnCheckedChangeListener(null)

                text = item.label
                isChecked = item.isSelected

                setOnCheckedChangeListener { _, isChecked ->
                    if (itemPosition != RecyclerView.NO_POSITION) {
                        callback.onItemToggle(item.id)
                    }
                }
            }
        }
    }

    interface Callback {
        fun onItemToggle(reasonId: String)
    }
}