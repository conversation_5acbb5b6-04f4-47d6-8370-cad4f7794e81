package com.bukuwarung.edc.replacement.domain.usecase

import android.content.Context
import android.net.Uri
import com.bukuwarung.edc.replacement.data.model.ReplacementEvidenceCategory
import com.bukuwarung.edc.replacement.data.model.ReplacementEvidenceResponse
import com.bukuwarung.edc.replacement.domain.ReplacementRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import javax.inject.Inject

class UploadReplacementEvidenceUseCase @Inject constructor(
    private val replacementRepository: ReplacementRepository,
    @ApplicationContext private val context: Context
) {
    suspend operator fun invoke(
        filePath: String,
        category: ReplacementEvidenceCategory
    ): Result<ReplacementEvidenceResponse> {
        return try {
            val file = getFileFromPath(filePath)
            if (!file.exists()) {
                return Result.failure(IllegalArgumentException("File not found: $filePath"))
            }

            val fileName = file.name
            val mimeType = when (category) {
                ReplacementEvidenceCategory.PHOTO -> "image/*"
                ReplacementEvidenceCategory.VIDEO -> "video/*"
            }

            val categoryBody = category.value.toRequestBody("text/plain".toMediaTypeOrNull())
            val fileNameBody = fileName.toRequestBody("text/plain".toMediaTypeOrNull())
            val requestFile = file.asRequestBody(mimeType.toMediaTypeOrNull())
            val filePart = MultipartBody.Part.createFormData("file", fileName, requestFile)

            val response = replacementRepository.uploadReplacementEvidence(
                category = categoryBody,
                fileName = fileNameBody,
                file = filePart
            )

            if (response.isSuccessful) {
                response.body()?.let {
                    Result.success(it)
                } ?: Result.failure(Exception("Empty response body"))
            } else {
                Result.failure(Exception("Upload failed: ${response.code()} - ${response.message()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    private fun getFileFromPath(filePath: String): File {
        return if (filePath.startsWith("content://")) {
            // Handle content URI
            val uri = Uri.parse(filePath)
            copyUriToFile(uri)
        } else {
            // Handle regular file path
            File(filePath)
        }
    }

    private fun copyUriToFile(uri: Uri): File {
        val inputStream: InputStream = context.contentResolver.openInputStream(uri)
            ?: throw IllegalArgumentException("Cannot open input stream for URI: $uri")

        val fileName = "temp_${System.currentTimeMillis()}.${getFileExtension(uri)}"
        val tempFile = File(context.cacheDir, fileName)

        inputStream.use { input ->
            FileOutputStream(tempFile).use { output ->
                input.copyTo(output)
            }
        }

        return tempFile
    }

    private fun getFileExtension(uri: Uri): String {
        val mimeType = context.contentResolver.getType(uri)
        return when {
            mimeType?.startsWith("video/") == true -> "mp4"
            mimeType?.startsWith("image/") == true -> "jpg"
            else -> "tmp"
        }
    }
}
