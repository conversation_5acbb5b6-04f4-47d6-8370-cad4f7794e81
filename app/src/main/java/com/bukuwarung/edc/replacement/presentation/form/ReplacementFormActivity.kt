package com.bukuwarung.edc.replacement.presentation.form

import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.media.ThumbnailUtils
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.ActivityReplacementFormBinding
import com.bukuwarung.edc.replacement.presentation.camera.ReplacementVideoCaptureActivity
import com.bukuwarung.edc.replacement.presentation.dialog.VideoPlaybackDialog
import com.bukuwarung.edc.replacement.presentation.form.ReplacementFormReasonAdapter
import com.bukuwarung.edc.util.CameraPermissionUtil
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class ReplacementFormActivity : AppCompatActivity() {

    private lateinit var binding: ActivityReplacementFormBinding

    private val viewModel: ReplacementFormViewModel by viewModels()
    private val reasonAdapter: ReplacementFormReasonAdapter by lazy {
        ReplacementFormReasonAdapter(callback = provideCallback())
    }

    // Activity result launcher for video capture
    private val videoCaptureResultLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            result.data?.getStringExtra(ReplacementVideoCaptureActivity.EXTRA_VIDEO_PATH)?.let { videoPath ->
                // Convert URI string to actual file path if needed
                val uri = Uri.parse(videoPath)
                val actualPath = getFilePathFromUri(uri) ?: videoPath
                viewModel.setUploadedVideo(actualPath)
            }
        }
    }

    private val colorDefault by lazy {
        ContextCompat.getColor(this, R.color.black_800)
    }

    private val colorRed by lazy {
        ContextCompat.getColor(this, android.R.color.holo_red_dark)
    }

    private val minChars by lazy {
        ReplacementFormViewModel.OTHER_REASON_MIN_CHARS
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityReplacementFormBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupView()
        observeUiState()
    }

    private fun setupView() {
        binding.apply {
            ivBack.setOnClickListener { handleBackPressed() }

            rvReplacementReasons.apply {
                adapter = reasonAdapter
                setHasFixedSize(true)
                isNestedScrollingEnabled = false
            }

            etOtherReason.addTextChangedListener(object : TextWatcher {
                override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                override fun afterTextChanged(s: Editable?) {
                    viewModel.updateOtherReasonText(s?.toString() ?: "")
                }
            })

            tvUploadVideoDevice.setOnClickListener {
                handleVideoUpload()
            }

            ivUploadedVideoDelete.setOnClickListener {
                viewModel.removeUploadedVideo()
            }

            tvUploadPhotoDevice.setOnClickListener {
                // [Adi] TODO: Implement photo upload
                handlePhotoUpload()
            }

            ivUploadedPhotoDelete.setOnClickListener {
                viewModel.removeUploadedPhoto()
            }

            btnNext.setOnClickListener { viewModel.submitForm() }
        }
        loadReplacementReasons()
    }

    private fun handleVideoUpload() {
        if (CameraPermissionUtil.hasVideoPermissions(this)) {
            launchVideoCapture()
        } else {
            CameraPermissionUtil.requestVideoPermissions(this)
        }
    }

    private fun launchVideoCapture() {
        val intent = ReplacementVideoCaptureActivity.createIntent(this)
        videoCaptureResultLauncher.launch(intent)
    }

    private fun handlePhotoUpload() {
        // [Adi] TODO: Implement actual photo upload logic
        viewModel.setUploadedPhoto("fake_photo_path.jpg")
    }

    private fun loadReplacementReasons() {
        viewModel.loadReplacementReasons()
    }

    private fun observeUiState() {
        lifecycleScope.launch {
            viewModel.uiState.collect { state ->
                updateUi(state)
            }
        }
    }

    private fun updateUi(state: ReplacementFormUiState) {
        binding.apply {
            tvDeviceTitle.text = state.deviceInfo.name
            tvDeviceTerminalNumber.text = state.deviceInfo.terminalNumber
            tvDeviceSerialNumber.text = state.deviceInfo.serialNumber
            tvUserIdNumber.text = state.deviceInfo.userId
        }

        val reasonsWithSelection = state.replacementReasons.map { reason ->
            reason.copy(isSelected = reason.id in state.selectedReasons)
        }
        reasonAdapter.submitList(reasonsWithSelection)

        updateSubmitButton(state.isSubmitButtonEnabled)
        updateUploadViews(state)

        updateMediaRequirementIndicator(state)
        updateOtherReasonViews(state)

        state.errorMessage?.let { error ->
            Toast.makeText(this@ReplacementFormActivity, error, Toast.LENGTH_LONG).show()
        }
    }

    private fun updateOtherReasonViews(state: ReplacementFormUiState) {
        binding.apply {
            tilOtherReason.visibility = if (state.isOtherReasonVisible) View.VISIBLE else View.GONE
            tvOtherReasonLabel.visibility = if (state.isOtherReasonVisible) View.VISIBLE else View.GONE

            if (state.isOtherReasonVisible) {
                val textLength = state.otherReasonText.length
                tilOtherReason.helperText = if (textLength < minChars) {
                    getString(R.string.replacement_other_reason_min_chars_helper, minChars, textLength, minChars)
                } else {
                    null
                }

                tilOtherReason.error = if (textLength in 1 until minChars) {
                    getString(R.string.replacement_other_reason_min_chars_error, minChars)
                } else {
                    null
                }

                tvOtherReasonLabel.setTextColor(
                    if (state.otherReasonText.length >= minChars) colorDefault else colorRed
                )
            } else {
                tvOtherReasonLabel.setTextColor(colorDefault)
                etOtherReason.text = null
            }
        }
    }

    private fun updateMediaRequirementIndicator(state: ReplacementFormUiState) {
        binding.apply {
            val videoLabel = if (state.isMediaRequired) {
                "${getString(R.string.replacement_upload_video_label)}$ASTERISK"
            } else {
                getString(R.string.replacement_upload_video_label)
            }

            val photoLabel = if (state.isMediaRequired) {
                "${getString(R.string.replacement_upload_photo_label)}$ASTERISK"
            } else {
                getString(R.string.replacement_upload_photo_label)
            }
            
            tvUploadVideoDeviceLabel.text = videoLabel
            tvUploadPhotoDeviceLabel.text = photoLabel

            if (state.isMediaRequired) {
                tvUploadVideoDeviceLabel.setTextColor(
                    if (state.uploadedVideoPath == null) colorRed else colorDefault
                )
                tvUploadPhotoDeviceLabel.setTextColor(
                    if (state.uploadedPhotoPath == null) colorRed else colorDefault
                )
            } else {
                tvUploadVideoDeviceLabel.setTextColor(colorDefault)
                tvUploadPhotoDeviceLabel.setTextColor(colorDefault)
            }
        }
    }

    private fun handleBackPressed() {
        super.onBackPressed()
    }

    private fun updateSubmitButton(isEnabled: Boolean) {
        binding.btnNext.apply {
            this.isEnabled = isEnabled
            backgroundTintList = ContextCompat.getColorStateList(
                context,
                if (isEnabled) R.color.bar_dashboard_ppob_3
                else R.color.switch_thumb_disabled_material_light
            )
            setTextColor(
                ContextCompat.getColor(
                    context,
                    if (isEnabled) R.color.black_800
                    else R.color.white
                )
            )
        }
    }

    private fun updateUploadViews(state: ReplacementFormUiState) {
        binding.apply {
            // Video upload state
            vfUploadVideoDevice.displayedChild = when {
                state.isUploadingVideo -> CHILD_MEDIA_UPLOADING
                state.uploadedVideoPath != null -> CHILD_MEDIA_UPLOADED
                else -> CHILD_UPLOAD_MEDIA
            }

            // Photo upload state
            vfUploadPhotoDevice.displayedChild = when {
                state.isUploadingPhoto -> CHILD_MEDIA_UPLOADING
                state.uploadedPhotoPath != null -> CHILD_MEDIA_UPLOADED
                else -> CHILD_UPLOAD_MEDIA
            }

            // Update video preview content
            if (state.uploadedVideoPath != null) {
                setupVideoPreview(state.uploadedVideoPath)
            }

            // Update photo preview content
            if (state.uploadedPhotoPath != null) {
                setupPhotoPreview(state.uploadedPhotoPath)
            }

            // Disable upload buttons while uploading
            tvUploadVideoDevice.isEnabled = !state.isUploadingVideo && !state.isUploadingPhoto
            tvUploadPhotoDevice.isEnabled = !state.isUploadingVideo && !state.isUploadingPhoto
        }
    }

    private fun provideCallback(): ReplacementFormReasonAdapter.Callback {
        return object : ReplacementFormReasonAdapter.Callback {
            override fun onItemToggle(reasonId: String) {
                viewModel.toggleReason(reasonId)
            }
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        CameraPermissionUtil.handleVideoPermissionResult(
            requestCode = requestCode,
            permissions = permissions,
            grantResults = grantResults,
            onGranted = { launchVideoCapture() },
            onDenied = {
                Toast.makeText(
                    this,
                    "Camera and microphone permissions are required to record video",
                    Toast.LENGTH_LONG
                ).show()
            }
        )
    }

    private fun setupVideoPreview(videoPath: String) {
        binding.apply {
            try {
                val uri = Uri.parse(videoPath)
                var thumbnail: Bitmap? = null

                // Try to generate thumbnail based on URI type
                if (videoPath.startsWith("content://")) {
                    // For content URIs, use MediaStore API
                    try {
                        thumbnail = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
                            // Use higher resolution for better quality
                            contentResolver.loadThumbnail(uri, android.util.Size(300, 200), null)
                        } else {
                            // Fallback for older versions with higher quality
                            val cursor = contentResolver.query(uri, arrayOf(MediaStore.Video.Media.DATA), null, null, null)
                            cursor?.use {
                                if (it.moveToFirst()) {
                                    val filePath = it.getString(it.getColumnIndexOrThrow(MediaStore.Video.Media.DATA))
                                    ThumbnailUtils.createVideoThumbnail(filePath, MediaStore.Images.Thumbnails.MICRO_KIND)
                                } else null
                            }
                        }
                    } catch (e: Exception) {
                        thumbnail = null
                    }
                } else {
                    // For file paths
                    thumbnail = ThumbnailUtils.createVideoThumbnail(videoPath, MediaStore.Images.Thumbnails.MINI_KIND)
                }

                if (thumbnail != null) {
                    // Set high-quality thumbnail
                    ivUploadedVideoContent.setImageBitmap(thumbnail)
                    ivUploadedVideoContent.scaleType = android.widget.ImageView.ScaleType.CENTER_CROP
                } else {
                    // Fallback to generic video icon
                    ivUploadedVideoContent.setImageResource(R.drawable.ic_video_file)
                    ivUploadedVideoContent.scaleType = android.widget.ImageView.ScaleType.CENTER_INSIDE
                }

                // Size is already set in XML layout

                // Setup delete button
                ivUploadedVideoDelete.setOnClickListener {
                    viewModel.removeUploadedVideo()
                }

                // Setup video thumbnail click to show video in dialog
                cvVideoThumbnail.setOnClickListener {
                    showVideoPlaybackDialog(videoPath)
                }



            } catch (e: Exception) {
                // Fallback to generic video icon
                ivUploadedVideoContent.setImageResource(R.drawable.ic_video_file)
                ivUploadedVideoContent.scaleType = android.widget.ImageView.ScaleType.CENTER_INSIDE

                ivUploadedVideoDelete.setOnClickListener {
                    viewModel.removeUploadedVideo()
                }

                // Setup video thumbnail click to show video in dialog (fallback case)
                cvVideoThumbnail.setOnClickListener {
                    showVideoPlaybackDialog(videoPath)
                }
            }
        }
    }

    private fun setupPhotoPreview(photoPath: String) {
        binding.apply {
            try {
                val uri = Uri.parse(photoPath)
                // For now, use a placeholder. In a real implementation, you'd load the actual image
                ivUploadedPhotoContent.setImageResource(R.drawable.ic_image_placeholder)
                ivUploadedPhotoContent.scaleType = android.widget.ImageView.ScaleType.CENTER_CROP

                // Setup delete button
                ivUploadedPhotoDelete.setOnClickListener {
                    viewModel.removeUploadedPhoto()
                }

            } catch (e: Exception) {
                ivUploadedPhotoContent.setImageResource(R.drawable.ic_image_placeholder)
                ivUploadedPhotoContent.scaleType = android.widget.ImageView.ScaleType.CENTER_CROP

                ivUploadedPhotoDelete.setOnClickListener {
                    viewModel.removeUploadedPhoto()
                }
            }
        }
    }

    private fun showVideoPlaybackDialog(videoPath: String) {
        val dialog = VideoPlaybackDialog.newInstance(videoPath)
        dialog.show(supportFragmentManager, "VideoPlaybackDialog")
    }

    private fun getFilePathFromUri(uri: Uri): String? {
        return try {
            // For content URIs, we'll use the URI string directly
            // The upload use case can handle both file paths and content URIs
            uri.toString()
        } catch (e: Exception) {
            null
        }
    }

    companion object {
        private const val CHILD_UPLOAD_MEDIA = 0
        private const val CHILD_MEDIA_UPLOADED = 1
        private const val CHILD_MEDIA_UPLOADING = 2
        private const val ASTERISK = "*"
    }

    object IntentExtras {
        const val DEVICE_NAME = "extra_device_name"
        const val DEVICE_TYPE = "extra_device_type"
        const val DEVICE_TERMINAL_NUMBER = "extra_device_terminal_number"
        const val DEVICE_SERIAL_NUMBER = "extra_device_serial_number"
        const val USER_ID = "extra_user_id"
    }
}

typealias ReplacementFormIntentExtras = ReplacementFormActivity.IntentExtras