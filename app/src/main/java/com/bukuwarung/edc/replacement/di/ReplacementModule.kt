package com.bukuwarung.edc.replacement.di

import com.bukuwarung.edc.replacement.data.ReplacementRepositoryImpl
import com.bukuwarung.edc.replacement.data.api.ReplacementApi
import com.bukuwarung.edc.replacement.domain.ReplacementRepository
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Named

@Module
@InstallIn(SingletonComponent::class)
class ReplacementModule {

    @Provides
    fun provideReplacementRepository(replacementApi: ReplacementApi): ReplacementRepository {
        return ReplacementRepositoryImpl(replacementApi)
    }

    @Provides
    fun provideReplacementApi(@Named("normal") retrofit: Retrofit): ReplacementApi {
        return retrofit.create(ReplacementApi::class.java)
    }
}
