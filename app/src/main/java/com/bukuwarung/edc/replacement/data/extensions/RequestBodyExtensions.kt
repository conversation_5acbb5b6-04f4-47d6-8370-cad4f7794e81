package com.bukuwarung.edc.replacement.data.extensions

import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.File

fun String.toPlainTextRequestBody(): RequestBody {
    return this.toRequestBody("text/plain".toMediaTypeOrNull())
}

fun File.toMultipartBodyPart(name: String, mimeType: String): MultipartBody.Part {
    val requestFile = this.asRequestBody(mimeType.toMediaTypeOrNull())
    return MultipartBody.Part.createFormData(name, this.name, requestFile)
}
