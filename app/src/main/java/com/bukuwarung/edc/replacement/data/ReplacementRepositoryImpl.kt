package com.bukuwarung.edc.replacement.data

import com.bukuwarung.edc.replacement.data.api.ReplacementApi
import com.bukuwarung.edc.replacement.data.model.ReplacementEvidenceResponse
import com.bukuwarung.edc.replacement.domain.ReplacementRepository
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Response
import javax.inject.Inject

class ReplacementRepositoryImpl @Inject constructor(
    private val replacementApi: ReplacementApi
) : ReplacementRepository {

    override suspend fun getReplacementReasons(deviceType: String) = replacementApi.getReplacementReasons(deviceType)
    
    override suspend fun uploadReplacementEvidence(
        category: RequestBody,
        fileName: RequestBody,
        file: MultipartBody.Part
    ): Response<ReplacementEvidenceResponse> = replacementApi.uploadReplacementEvidence(category, fileName, file)
}
