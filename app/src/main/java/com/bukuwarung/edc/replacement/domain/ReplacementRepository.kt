package com.bukuwarung.edc.replacement.domain

import com.bukuwarung.edc.replacement.data.model.ReplacementEvidenceResponse
import com.bukuwarung.edc.replacement.data.model.ReplacementReasonResponse
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.Response

interface ReplacementRepository {
    suspend fun getReplacementReasons(deviceType: String): Response<ReplacementReasonResponse>
    
    suspend fun uploadReplacementEvidence(
        category: RequestBody,
        fileName: RequestBody,
        file: MultipartBody.Part
    ): Response<ReplacementEvidenceResponse>
}
