package com.bukuwarung.edc.replacement.presentation.form

data class ReplacementFormDeviceInfo(
    val type: ReplacementDeviceType = ReplacementDeviceType.UNKNOWN,
    val name: String = "",
    val terminalNumber: String = "",
    val serialNumber: String = "",
    val userId: String = ""
)

enum class ReplacementDeviceType(val value: String) {
    ANDROID("ANDROID"),
    SAKU("SAKU"),
    UNKNOWN("");

    companion object {
        fun fromValue(value: String?): ReplacementDeviceType {
            if (value.isNullOrBlank()) return UNKNOWN
            return values().find { it.value == value } ?: UNKNOWN
        }
    }
}

fun String?.toReplacementDeviceType(): ReplacementDeviceType {
    return ReplacementDeviceType.fromValue(this)
}

