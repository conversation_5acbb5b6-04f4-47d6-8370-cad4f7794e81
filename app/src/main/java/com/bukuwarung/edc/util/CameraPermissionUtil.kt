package com.bukuwarung.edc.util

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat

object CameraPermissionUtil {

    const val CAMERA_PERMISSION_REQUEST_CODE = 1001
    const val VIDEO_PERMISSION_REQUEST_CODE = 1002

    private val CAMERA_PERMISSIONS = arrayOf(
        Manifest.permission.CAMERA
    )

    private val VIDEO_PERMISSIONS = arrayOf(
        Manifest.permission.CAMERA,
        Manifest.permission.RECORD_AUDIO
    )

    /**
     * Check if camera permission is granted
     */
    fun hasCameraPermission(context: Context): Boolean {
        return CAMERA_PERMISSIONS.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * Check if video recording permissions are granted
     */
    fun hasVideoPermissions(context: Context): Boolean {
        return VIDEO_PERMISSIONS.all { permission ->
            ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED
        }
    }

    /**
     * Request camera permission
     */
    fun requestCameraPermission(activity: Activity) {
        ActivityCompat.requestPermissions(
            activity,
            CAMERA_PERMISSIONS,
            CAMERA_PERMISSION_REQUEST_CODE
        )
    }

    /**
     * Request video recording permissions
     */
    fun requestVideoPermissions(activity: Activity) {
        ActivityCompat.requestPermissions(
            activity,
            VIDEO_PERMISSIONS,
            VIDEO_PERMISSION_REQUEST_CODE
        )
    }

    /**
     * Check if we should show permission rationale
     */
    fun shouldShowCameraPermissionRationale(activity: Activity): Boolean {
        return CAMERA_PERMISSIONS.any { permission ->
            ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)
        }
    }

    /**
     * Check if we should show video permission rationale
     */
    fun shouldShowVideoPermissionRationale(activity: Activity): Boolean {
        return VIDEO_PERMISSIONS.any { permission ->
            ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)
        }
    }

    /**
     * Handle permission result for camera
     */
    fun handleCameraPermissionResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        when (requestCode) {
            CAMERA_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && 
                    grantResults.all { it == PackageManager.PERMISSION_GRANTED }) {
                    onGranted()
                } else {
                    onDenied()
                }
            }
        }
    }

    /**
     * Handle permission result for video
     */
    fun handleVideoPermissionResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray,
        onGranted: () -> Unit,
        onDenied: () -> Unit
    ) {
        when (requestCode) {
            VIDEO_PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && 
                    grantResults.all { it == PackageManager.PERMISSION_GRANTED }) {
                    onGranted()
                } else {
                    onDenied()
                }
            }
        }
    }
}
