package com.bukuwarung.edc.payments.ui.core

import android.os.CountDownTimer
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.enums.AuthActions
import com.bukuwarung.edc.global.network.interceptors.NoConnectivityException
import com.bukuwarung.edc.payments.constant.PaymentConst
import com.bukuwarung.edc.payments.constant.PinType
import com.bukuwarung.edc.payments.data.model.request.PinForgetRequest
import com.bukuwarung.edc.payments.data.model.request.PinSetupRequest
import com.bukuwarung.edc.payments.data.model.request.PinUpdateRequest
import com.bukuwarung.edc.payments.usecase.PaymentTwoFAUseCase
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.errorMessage
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.orDefault
import com.bukuwarung.edc.util.put
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class NewPaymentPinViewModel @Inject constructor(
    private var paymentTwoFAUseCase: PaymentTwoFAUseCase
) : ViewModel() {

    private var lastPinChangeDate = ""
    var otpChannel = Constant.SMS
    private var createPinViewSelected = false
    private var confirmPinViewSelected = false
    private var currentPin = ""
    private var createNewPin = ""
    private var confirmNewPin = ""
    private var pinLength: Int = 6
    private var useCase: PinType = PinType.PIN_CONFIRM
    private var timer: CountDownTimer? = null
    private val _viewSharedFlow = MutableSharedFlow<ViewState>(replay = 1)
    val viewSharedFlow = _viewSharedFlow.asSharedFlow()

    data class ViewState(
        val showLoader: Boolean = false,
        val showOtpView: Boolean = false,
        val showConfirmPinView: Boolean = false,
        val showPinUpdateView1: Boolean = false,
        val showPinUpdateView2: Boolean = false,
        val createPinViewSelected: Boolean = false,
        val confirmPinViewSelected: Boolean = false,
        val enablePinUpdateButton: Boolean = false,
        val otpError: String = "",
        val pinApiError: String = "",
        val otpTimeInSecs: Int? = null,
        val pinConfirmationError: String = "",
        val pinPatternError: Boolean = false,
        val pinMatchingOldPin: Boolean = false,
        val pinUpdateError: String = "",
        val pinLength: Int = 6,
        val currentPin: String = "",
        val createNewPin: String = "",
        val confirmNewPin: String = "",
        val pinConfirmationSuccessful: Boolean = false,
        val internetError: Boolean = false,
        val serverError: Boolean = false,
        val errorContent: String = "",
        val livelinessExpired: Boolean = false,
        val checkoutToken: String = "",
        val otpChannel: String = Constant.SMS,
        val lastPinChangeDate: String = "",
        val changePinStatus: String = "",
        val checkChangePinStatus: Boolean = false
    )

    fun onEventReceived(paymentPinIntent: PaymentPinIntent) {
        when (paymentPinIntent) {
            is PaymentPinIntent.OnCreateView -> handleOnCreateView(paymentPinIntent.useCase, paymentPinIntent.skipOtpCall)
            is PaymentPinIntent.OnVerifyOTP -> verifyOtp(paymentPinIntent.otp)
            is PaymentPinIntent.OnRequestOTP -> requestOtp(paymentPinIntent.forceRequest)
            is PaymentPinIntent.AddDigit -> addDigitToPin(paymentPinIntent.digit)
            PaymentPinIntent.DeleteDigit -> deleteDigitFromPin()
            PaymentPinIntent.ConfirmNewPin -> {
                when(useCase){
                    PinType.PIN_CREATE, PinType.PIN_CONFIRM -> createPin()
                    PinType.PIN_UPDATE -> updatePin()
                    PinType.PIN_FORGOT, PinType.PIN_FORGOT_ENTER_OTP_STEP, PinType.PIN_FORGOT_CREATE_PIN_STEP -> forgotPin()
                }
            }
            PaymentPinIntent.ClickedCreatePinView -> pinViewSelected(isCreatePinView = true)
            PaymentPinIntent.ClickedConfirmPinView -> pinViewSelected(isCreatePinView = false)
            PaymentPinIntent.ChangeOtpChannel -> changeOtpChannel()
        }
    }
    private fun pinViewSelected(isCreatePinView: Boolean) = viewModelScope.launch {
        createPinViewSelected = isCreatePinView
        confirmPinViewSelected = !isCreatePinView
        _viewSharedFlow.tryEmit(ViewState(showPinUpdateView1 = true, showPinUpdateView2 = true, createPinViewSelected = createPinViewSelected, confirmPinViewSelected = confirmPinViewSelected, createNewPin = createNewPin, confirmNewPin = confirmNewPin))
    }

    private fun addDigitToPin(digit: Int) = viewModelScope.launch {
        if (createPinViewSelected){
            if (createNewPin.length < 6) createNewPin = createNewPin.plus(digit)
            _viewSharedFlow.emit(ViewState(createNewPin = createNewPin, showPinUpdateView1 = true, createPinViewSelected = createPinViewSelected))
            if (createNewPin.length == 6){
                _viewSharedFlow.emit(ViewState(showLoader = true))
                if (createNewPin == currentPin) {
                    _viewSharedFlow.emit(ViewState(createNewPin = createNewPin, showPinUpdateView1 = true, createPinViewSelected = createPinViewSelected, pinMatchingOldPin = true))
                } else if (Utils.checkIfPinNotAllowed(createNewPin)){
                    _viewSharedFlow.emit(ViewState(createNewPin = createNewPin, showPinUpdateView1 = true, createPinViewSelected = createPinViewSelected, pinPatternError = true))
                } else {
                    confirmPinViewSelected = true
                    createPinViewSelected = false
                    _viewSharedFlow.emit(ViewState(createNewPin = createNewPin, showPinUpdateView1 = true, showPinUpdateView2 = true, createPinViewSelected = createPinViewSelected, confirmPinViewSelected = confirmPinViewSelected))
                }
            }
        } else if (confirmPinViewSelected) {
            if (confirmNewPin.length < 6) confirmNewPin = confirmNewPin.plus(digit)
            _viewSharedFlow.emit(ViewState(createNewPin = createNewPin, confirmNewPin = confirmNewPin, showPinUpdateView1 = true, showPinUpdateView2 = true, createPinViewSelected = createPinViewSelected, confirmPinViewSelected = confirmPinViewSelected))
            if (confirmNewPin.length == 6){
                if (createNewPin == confirmNewPin) {
                    _viewSharedFlow.emit(ViewState(createNewPin = createNewPin, confirmNewPin = confirmNewPin, showPinUpdateView1 = true, showPinUpdateView2 = true, createPinViewSelected = createPinViewSelected, confirmPinViewSelected = confirmPinViewSelected, enablePinUpdateButton = true))
                } else {
                    showPinErrorView("Konfirmasi PIN tidak sesuai. Silakan cek ulang.")
                }

            }
        } else {
            if (currentPin.length < pinLength) currentPin = currentPin.plus(digit)
            _viewSharedFlow.emit(ViewState(showConfirmPinView = true, pinLength = pinLength, currentPin = currentPin, lastPinChangeDate = lastPinChangeDate))
            if (currentPin.length == pinLength){
                _viewSharedFlow.emit(ViewState(showLoader = true))
                try {
                    paymentTwoFAUseCase.verifyPin(currentPin).let {
                        if (it.isSuccessful){
                            if (useCase == PinType.PIN_UPDATE)
                                requestOtp()
                            else
                                _viewSharedFlow.emit(ViewState(pinConfirmationSuccessful = true, checkoutToken = it.body()?.token.orEmpty()))
                        } else {
                            currentPin = ""
                            _viewSharedFlow.emit(ViewState(showConfirmPinView = true, lastPinChangeDate = lastPinChangeDate, pinLength = pinLength, pinConfirmationError = it.errorMessage()))
                        }
                    }
                } catch (e: Exception){
                    currentPin = ""
                    _viewSharedFlow.emit(ViewState(showConfirmPinView = true, lastPinChangeDate = lastPinChangeDate, pinLength = pinLength, pinConfirmationError = e.message.toString()))
                }
            }
        }
    }

    private fun deleteDigitFromPin() = viewModelScope.launch{
        if (createPinViewSelected){
            createNewPin = createNewPin.dropLast(1)
            confirmNewPin = ""
            _viewSharedFlow.emit(ViewState(createNewPin = createNewPin, confirmNewPin = confirmNewPin, showPinUpdateView1 = true, createPinViewSelected = createPinViewSelected, confirmPinViewSelected = confirmPinViewSelected))
        } else if (confirmPinViewSelected){
            confirmNewPin = confirmNewPin.dropLast(1)
            _viewSharedFlow.emit(ViewState(createNewPin = createNewPin, confirmNewPin = confirmNewPin, showPinUpdateView1 = true, showPinUpdateView2 = true, createPinViewSelected = createPinViewSelected, confirmPinViewSelected = confirmPinViewSelected))
        } else {
            currentPin = currentPin.dropLast(1)
            _viewSharedFlow.emit(ViewState(showConfirmPinView = true, lastPinChangeDate = lastPinChangeDate, pinLength = pinLength, currentPin = currentPin))
        }
    }

    private fun handleOnCreateView(useCase: PinType, skipOtpCall: Boolean) {
        <EMAIL> = useCase
        when(useCase){
            PinType.PIN_FORGOT_ENTER_OTP_STEP -> { requestOtp() }
            PinType.PIN_FORGOT_CREATE_PIN_STEP -> { showPinUpdateView1() }
            PinType.PIN_UPDATE -> { checkPinLength() }
            PinType.PIN_CREATE -> { requestOtp() }
            PinType.PIN_CONFIRM -> { checkPinLength() }
            PinType.PIN_FORGOT -> { requestOtp(skipOtpCall) }
        }
    }

    private fun showPinUpdateView1() = viewModelScope.launch{
        createPinViewSelected = true
        _viewSharedFlow.emit(ViewState(showPinUpdateView1 = true, createPinViewSelected = createPinViewSelected))
    }

    private fun checkPinLength() = viewModelScope.launch{
        _viewSharedFlow.emit(ViewState(showLoader = true))
        try {
            paymentTwoFAUseCase.checkPinLength().let {
                if (it.isSuccessful){
                    if (it.body()?.success.isTrue){
                        pinLength = it.body()?.pinLength.orDefault(6)
                        lastPinChangeDate = it.body()?.lastPinChangeDate.orEmpty()
                        _viewSharedFlow.emit(ViewState(showConfirmPinView = true,  lastPinChangeDate = lastPinChangeDate, pinLength = pinLength))
                    } else {
                        requestOtp()
                    }
                } else {
                    _viewSharedFlow.emit(ViewState(serverError = true, errorContent = it.errorMessage()))
                }
            }
        } catch (e: NoConnectivityException){
            _viewSharedFlow.emit(ViewState(internetError = true, errorContent = e.message))
        } catch (e: Exception){
            _viewSharedFlow.emit(ViewState(serverError = true, errorContent = e.message.orEmpty()))
        }
    }

    private fun requestOtp(skipApiCall: Boolean = false) = viewModelScope.launch {
        if (skipApiCall){
            setUiForOtp()
        } else {
            _viewSharedFlow.emit(ViewState(showLoader = true))
            try {
                paymentTwoFAUseCase.requestOtpForPIN(otpChannel, getAuthAction()).let {
                    if (it.isSuccessful){
                        Utils.sharedPreferences.put("op_token", it.body()?.token)
                        setUiForOtp()
                    } else {
                        if (it.code() == 409 || it.code() == 402) _viewSharedFlow.emit(ViewState(livelinessExpired = true))
                        else _viewSharedFlow.emit(ViewState(showOtpView = true,otpError = it.errorMessage(), otpChannel = otpChannel))
                    }
                }
            } catch (e: Exception) {
                _viewSharedFlow.emit(ViewState(showOtpView = true,otpError = e.message.orEmpty(), otpChannel = otpChannel))
            }
        }
    }

    private fun setUiForOtp() = viewModelScope.launch{
        _viewSharedFlow.emit(ViewState(showOtpView = true,otpTimeInSecs = PaymentConst.OTP_WAIT_TIME_IN_SECONDS, otpChannel = otpChannel))
        val waitTime = PaymentConst.OTP_WAIT_TIME_IN_SECONDS
        timer = object :
            CountDownTimer(waitTime * Constant.ONE_SECOND, Constant.ONE_SECOND) {
            override fun onTick(millisUntilFinished: Long) {
                viewModelScope.launch{
                    _viewSharedFlow.emit(ViewState(showOtpView = true, otpTimeInSecs = (millisUntilFinished / Constant.ONE_SECOND).toInt(), otpChannel = otpChannel))
                }
            }

            override fun onFinish() {
                viewModelScope.launch{
                    _viewSharedFlow.emit(ViewState(showOtpView = true, otpChannel = otpChannel))
                }
            }
        }
        timer!!.start()
    }

    private fun verifyOtp(otp: String) = viewModelScope.launch{
        timer?.cancel()
        //show loader
        _viewSharedFlow.emit(ViewState(showLoader = true))
        try {
            paymentTwoFAUseCase.verifyOtp(otp, getAuthAction()).let {
                if (it.isSuccessful) {
                    Utils.sharedPreferences.put("op_token", it.body()?.token)
                    showPinUpdateView1()
                } else {
                    _viewSharedFlow.emit(ViewState(showOtpView = true, otpChannel = otpChannel, otpError = it.errorMessage()))
                }
            }
        } catch (e: Exception){
            _viewSharedFlow.emit(ViewState(showOtpView = true, otpChannel = otpChannel, otpError = e.message.orEmpty()))
        }
    }

    private fun getAuthAction(): String{
        return when(useCase){
            PinType.PIN_CREATE, PinType.PIN_CONFIRM -> AuthActions.CREATE_SALDO_PIN_V3.action
            PinType.PIN_UPDATE -> AuthActions.UPDATE_SALDO_PIN_V3.action
            PinType.PIN_FORGOT, PinType.PIN_FORGOT_CREATE_PIN_STEP, PinType.PIN_FORGOT_ENTER_OTP_STEP -> AuthActions.FORGOT_SALDO_PIN_V3.action
        }
    }

    private fun showPinErrorView(message: String) = viewModelScope.launch{
        _viewSharedFlow.emit(ViewState(showPinUpdateView1 = true, showPinUpdateView2 = true, enablePinUpdateButton = false, pinUpdateError = message, createNewPin = createNewPin, confirmNewPin = confirmNewPin, createPinViewSelected = createPinViewSelected, confirmPinViewSelected = confirmPinViewSelected))
    }

    private fun createPin() = viewModelScope.launch{
        _viewSharedFlow.emit(ViewState(showLoader = true))
        val pinCreateRequest = PinSetupRequest(pin = confirmNewPin)
        try {
            paymentTwoFAUseCase.createPinV3(pinCreateRequest).let {
                if (it.isSuccessful){
                    _viewSharedFlow.emit(ViewState(pinConfirmationSuccessful = true, checkoutToken = it.body()?.token.orEmpty()))
                } else {
                    if (it.code() == 409 || it.code() == 402) _viewSharedFlow.emit(ViewState(livelinessExpired = true))
                    else showPinErrorView(it.errorMessage())
                }
            }
        } catch (e: Exception){
            showPinErrorView(e.message.toString())
        }
    }

    private fun updatePin() = viewModelScope.launch{
        _viewSharedFlow.emit(ViewState(showLoader = true))
        val pinUpdateRequest = PinUpdateRequest(oldPin = currentPin, newPin = confirmNewPin)
        try {
            paymentTwoFAUseCase.updatePinV3(pinUpdateRequest).let {
                if (it.isSuccessful){
                    _viewSharedFlow.emit(ViewState(pinConfirmationSuccessful = true))
                } else {
                    if (it.code() == 409 || it.code() == 402) _viewSharedFlow.emit(ViewState(livelinessExpired = true))
                    else showPinErrorView(it.errorMessage())
                }
            }
        } catch (e: Exception){
            showPinErrorView(e.message.toString())
        }
    }

    private fun forgotPin() = viewModelScope.launch{
        _viewSharedFlow.emit(ViewState(showLoader = true))
        val pinForgotPin = PinForgetRequest(pin = confirmNewPin)
        try {
            paymentTwoFAUseCase.forgotPinV3(pinForgotPin).let {
                if (it.isSuccessful){
                    _viewSharedFlow.emit(ViewState(pinConfirmationSuccessful = true))
                } else {
                    if (it.code() == 409 || it.code() == 402) _viewSharedFlow.emit(ViewState(livelinessExpired = true))
                    else showPinErrorView(it.errorMessage())

                }
            }
        } catch (e: Exception){
            showPinErrorView(e.message.toString())
        }
    }

    private fun changeOtpChannel(){
        otpChannel = if (otpChannel == Constant.SMS) Constant.WA else Constant.SMS
        requestOtp()
    }

    fun checkPinChangeRequest() = viewModelScope.launch(Dispatchers.IO) {
        try {
            paymentTwoFAUseCase.checkPinChangeRequest().let {
                if (it.isSuccessful) {
                    _viewSharedFlow.emit(ViewState(showConfirmPinView = true, pinLength = pinLength, currentPin = currentPin, lastPinChangeDate = lastPinChangeDate, checkChangePinStatus = true, changePinStatus = it.body()?.content?.firstOrNull()?.status.orEmpty()))
                } else {
                    _viewSharedFlow.emit(ViewState(showConfirmPinView = true, pinLength = pinLength, currentPin = currentPin, lastPinChangeDate = lastPinChangeDate, checkChangePinStatus = true, changePinStatus = ""))
                }
            }
        } catch (e: Exception) {
            _viewSharedFlow.emit(ViewState(showConfirmPinView = true, pinLength = pinLength, currentPin = currentPin, lastPinChangeDate = lastPinChangeDate, checkChangePinStatus = true, changePinStatus = ""))
        }
    }
}