package com.bukuwarung.edc.payments.ui.widgets

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import com.bukuwarung.edc.R
import com.bukuwarung.edc.databinding.OrderPaymentInfoViewBinding
import com.bukuwarung.edc.payments.data.model.OrderResponse
import com.bukuwarung.edc.payments.util.PaymentAuxilliary
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isNotZero
import com.bukuwarung.edc.util.orNil
import com.bukuwarung.edc.util.showView


class OrderPaymentInfoView(context: Context, attrs: AttributeSet) :
    ConstraintLayout(context, attrs) {

    private val binding: OrderPaymentInfoViewBinding =
        OrderPaymentInfoViewBinding.inflate(LayoutInflater.from(context), this, true)

    fun setView(order: OrderResponse, paymentType: String?) {
        if (!PaymentAuxilliary.isPaymentOut(paymentType)) {
            this.hideView()
            return
        }
        val item = order.items?.firstOrNull()
        with(binding) {
            tvTotalReceivedValue.text =
                Utils.formatAmount(PaymentAuxilliary.getTotalReceivedAmount(order, item))
            setFeeView(order)
            tvLoyaltyDiscount.text =
                context.getString(R.string.discount_level, order.loyalty?.tierName.orEmpty())
            tvLoyaltyDiscountValue.text = "-${Utils.formatAmount(order.loyalty?.tierDiscount)}"
            grpLoyaltyDiscount.visibility = order.loyalty?.tierDiscount.orNil.isNotZero().asVisibility()
            tvTotalPaymentValue.text = Utils.formatAmount(item?.amount)

            val paymentMethodName = order.payments?.firstOrNull()?.paymentMethod?.name
            tvPaymentMethodValue.text = paymentMethodName

            tvPaymentCategoryValue.text = order.paymentCategory?.name ?: "-"

            tvNotesValue.text = order.description ?: "-"

            ivDetailCollapse.setOnClickListener {
                if (clDetailsContainer.isVisible) {
                    clDetailsContainer.hideView()
                    ivDetailCollapse.setImageResource(R.drawable.ic_chevron_down)
                } else {
                    clDetailsContainer.showView()
                    ivDetailCollapse.setImageResource(R.drawable.ic_chevron_up)
                }
            }

            <EMAIL>()
        }
    }

    private fun setFeeView(order: OrderResponse) {
        val fee = order.items?.firstOrNull()?.fee
        val discountedFee = order.items?.firstOrNull()?.discountedFee
        with(binding) {
            when (fee) {
                0.0 -> {
                    tvFeeValue.hideView()
                    tvDiscountedFeeValue.text = context.getString(R.string.free).uppercase()
                }
                discountedFee -> {
                    tvFeeValue.hideView()
                    tvDiscountedFeeValue.text = Utils.formatAmount(fee)
                }
                else -> {
                    tvFeeValue.text = Utils.formatAmount(fee)
                    tvDiscountedFeeValue.text = Utils.formatAmount(discountedFee)
                }
            }
        }
    }
}
