package com.bukuwarung.edc.payments.data.model

import android.os.Parcelable
import androidx.annotation.Keep
import com.bukuwarung.edc.ppob.constants.PpobConst
import com.bukuwarung.edc.util.isTrue
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class BankAccount(
    @SerializedName("bank_account_id")
    val bankAccountId: String = "",
    @SerializedName("account_id")
    val accountId: String? = null,
    @SerializedName("bank_code")
    val bankCode: String? = null,
    @SerializedName("bank_name")
    val bankName: String? = null,
    @SerializedName("account_number")
    val accountNumber: String? = null,
    @SerializedName("customer_id")
    val customerId: String? = "",
    @SerializedName("account_holder_name")
    val accountHolderName: String? = null,
    @SerializedName("is_disabled")
    val isDisabled: Boolean? = null,
    var isSelected: Int = 0,
    var flag: String? = null,
    var message: String? = null,
    var logo: String? = null,

    @SerializedName("bank_logo")
    val bankLogo:String?=null,

    @SerializedName("matching_status")
    var matchingStatus: Boolean? = null,

    @SerializedName("account_already_exists")
    var accountAlreadyExists: Boolean? = null,

    @SerializedName("manual_verification_status")
    val manualVerificationStatus: String? = null,

    @field:SerializedName("status")
    val status: String? = null,

    @field:SerializedName("payment_bank_code")
    val paymentBankCode: String? = null,
    @SerializedName("manual_matching_info")
    var manualMatchingInfo: ManualMatchingInfo? = null,

    @SerializedName("auto_matching_info")
    var autoMatchingInfo: AutoMatchingInfo? = null,



    ) : Parcelable

@Keep
@Parcelize
data class AutoMatchingInfo(
    @SerializedName("is_auto_name_matching")
    var isAutoNameMatching: Boolean? = false,
    @SerializedName("auto_matching_status")
    var autoMatchingStatus: AutoMatchingStatus? = null,
    @SerializedName("matched_name")
    var matchedName: String? = null
): Parcelable

@Keep
@Parcelize
data class ManualMatchingInfo(
    @SerializedName("is_manual_verification")
    var isManualVerification: Boolean? = false,
    @SerializedName("manual_matching_status")
    var manualMatchingStatus: ManualMatchingStatus? = null,
    @SerializedName("rejected_reason")
    var rejectedReason: String? = null
): Parcelable


fun BankAccount.isAccountSelected() = isSelected != 0
fun BankAccount.getBankLogoIfAvailable(): String? {
    val currentBank = Bank(bankCode ?: "", "")
    return if (Bank.BANKS.contains(currentBank)) {
        Bank.BANKS.first { it.bankCode == currentBank.bankCode }.logo
    } else {
        null
    }
}

fun BankAccount.getActiveAccount(): Boolean = flag?.equals(PpobConst.ACTIVE, ignoreCase = true) ?: false

fun BankAccount.getInActiveAccount(): Boolean = flag?.equals(PpobConst.IN_ACTIVE, ignoreCase = true) ?: false

fun BankAccount.isMatchingFailure(): Boolean {
    return when {
        autoMatchingInfo?.isAutoNameMatching.isTrue -> {
            autoMatchingInfo?.autoMatchingStatus == AutoMatchingStatus.FAILED
        }
        manualMatchingInfo?.isManualVerification.isTrue -> {
            manualMatchingInfo?.manualMatchingStatus == ManualMatchingStatus.REJECTED
        }
        else -> false
    }
}

fun BankAccount.getNotSupportedAccount(): Boolean = flag?.equals(PpobConst.NOT_SUPPORTED, ignoreCase = true) ?: false

fun BankAccount.isManualMatchingRequired(): Boolean =
    autoMatchingInfo?.autoMatchingStatus == AutoMatchingStatus.FAILED
            && manualMatchingInfo == null

fun BankAccount.isManualMatchingInProgress(): Boolean =
    manualMatchingInfo?.manualMatchingStatus == ManualMatchingStatus.UNVERIFIED

fun BankAccount.isManualMatchingRejected(): Boolean =
    manualMatchingInfo?.manualMatchingStatus == ManualMatchingStatus.REJECTED

@Keep
enum class AutoMatchingStatus {
    SUCCESS, FAILED
}

@Keep
enum class ManualMatchingStatus {
    UNVERIFIED, VERIFIED, REJECTED
}

enum class BankAccountStatus {
    VALID, INVALID, MANUAL_VERIFICATION
}

enum class UrlType {
    APPEAL_FLOW, MATCHING_INFO, FAQ_USED_ACCOUNT, FAQ_BLOCKED_ACCOUNT
}

