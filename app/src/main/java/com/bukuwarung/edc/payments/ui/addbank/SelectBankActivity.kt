package com.bukuwarung.edc.payments.ui.addbank

import android.app.Activity
import android.os.Bundle
import android.util.Log
import android.view.MenuItem
import android.widget.Toast
import androidx.activity.viewModels
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.edc.R
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants.BANK_DESTINATION
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants.BUTTON
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants.EVENT_BACK_BUTTON_CLICK
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants.PAGE
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants.SELECT_BANK_DESTINATION
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants.TRANSACTION_TYPE
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.transfermoney.ui.AddBankAccountMoneyTransferActivity
import com.bukuwarung.edc.card.ui.EdcCardViewModel
import com.bukuwarung.edc.card.ui.ErrorMapping
import com.bukuwarung.edc.databinding.ActivitySelectBankBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.base.BaseCardActivity
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.global.network.Status
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.payments.constant.PaymentRemoteConfig
import com.bukuwarung.edc.payments.data.model.Bank
import com.bukuwarung.edc.payments.data.model.BankAccount
import com.bukuwarung.edc.payments.data.model.CardErrorType
import com.bukuwarung.edc.payments.ui.addbank.AddBankAccountActivity.Companion.SELECTED_BANK
import com.bukuwarung.edc.payments.ui.dialog.CardErrorDialog
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.afterTextChanged
import com.bukuwarung.edc.util.goToDestination
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.isTrue
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.showView
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class SelectBankActivity : BaseCardActivity(), BankListAdapter.Callback {

    companion object {
        const val IS_MONEY_TRANSFER = "IS_MONEY_TRANSFER"
        const val TRANSACTION_TYPE = "transaction_type"
        const val BANK_STATUS_INACTIVE = "INACTIVE"
        const val BANK_STATUS_QUERY = "ACTIVE,INACTIVE"
    }

    private val bankViewModel: BankViewModel by viewModels()

    private lateinit var binding: ActivitySelectBankBinding
    private var banksAdapter: BankListAdapter? = null
    private var bundle: Bundle? = null
    private val isMoneyTransfer by lazy {
        intent?.getBooleanExtra(IS_MONEY_TRANSFER, false) ?: false
    }
    private val transactionType by lazy {
        intent?.getStringExtra(TRANSACTION_TYPE) ?: TransactionType.TRANSFER_INQUIRY.type
    }
    private var isRemovalDialogShown = false
    private var errorDialog: CardErrorDialog? = null

    override fun setViewBinding() {
        binding = ActivitySelectBankBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {

        binding.toolbar.apply {
            tvTitle.text = getString(R.string.select_bank)
            btnBack.setOnClickListener {
                trackBackPressEvent(CardAnalyticsConstants.TOP_ARROW_BACK_BUTTON)
                handleBackPressed()
            }
        }
        if (intent.hasExtra("data")) {
            bundle = intent.getBundleExtra("data")
        }

        with(binding) {
            etBankSearch.afterTextChanged {
                banksAdapter?.filter?.filter(it)
                if (it.isBlank()) {
                    ivSearchClear.hideView()
                } else {
                    ivSearchClear.showView()
                }
            }
            ivSearchClear.setOnClickListener {
                etBankSearch.text?.clear()
            }
        }

        errorDialog = CardErrorDialog(
            context = this,
            errorType = CardErrorType.REMOVE_CARD,
            errorCode = ErrorMapping.removeCardErrorCode[0]
        )

        Log.d("--->", "transactionType: $transactionType")
        when {
            isMoneyTransfer -> {
                bankViewModel.getEdcBanks(Utils.getPaymentAccountId(), "", BANK_STATUS_QUERY)
            }

            transactionType == TransactionType.CASH_WITHDRAWAL.type -> {
                bankViewModel.getEdcBanks(Utils.getPaymentAccountId(), "settlement", BANK_STATUS_QUERY)
            }

            else -> {
                bankViewModel.getBanks()
            }
        }
    }

    override fun subscribeState() {
        bankViewModel.banks.observe(this) {
            when (it.status) {
                Status.SUCCESS -> {
                    binding.gpEmptyList.hideView()
                    binding.pbBanks.hideView()
                    it.data?.let { banks ->
                        binding.etBankSearch.showView()
                        if (banks.isEmpty()) {
                            binding.gpEmptyList.showView()
                        } else {
                            binding.gpEmptyList.hideView()
                            banksAdapter = BankListAdapter(banks, banks, this)
                            binding.rvBanks.apply {
                                layoutManager = LinearLayoutManager(this@SelectBankActivity)
                                adapter = banksAdapter
                            }
                            showWarningMessage(banks)
                        }
                    }
                }

                Status.ERROR -> {
                    binding.pbBanks.hideView()
                    binding.gpEmptyList.showView()
                    Toast.makeText(this, it.message, Toast.LENGTH_SHORT).show()
                }

                Status.LOADING -> {
                    binding.gpEmptyList.hideView()
                    binding.pbBanks.showView()
                }

                Status.NO_INTERNET -> {
                    binding.gpEmptyList.showView()
                    binding.pbBanks.hideView()
                    if (errorDialog != null && errorDialog!!.isShowing) {
                        errorDialog?.dismiss()
                        errorDialog = null
                    }
                    errorDialog = CardErrorDialog(
                        context = this,
                        errorType = CardErrorType.NO_INTERNET,
                        errorCode = ErrorMapping.noInternetErrorCode[0]
                    ) {
                        checkCardAndShowDialog()
                    }
                    Utils.showDialogIfActivityAlive(this, errorDialog)
                }
            }
        }

        edcCardViewModel.checkCardResult.observe(this) {
            when (it?.status) {
                Constants.CARD_STATUS_UNSUPPORTED -> {
                    edcCardViewModel.stopCheckCard()
                    goToDestination(HomePageActivity::class.java)
                }

                Constants.CARD_STATUS_VALID -> {
                    edcCardViewModel.onEventReceived(
                        EdcCardViewModel.Event.OnStartCheckCard(
                            1, false
                        )
                    )
                    if (!errorDialog?.isShowing.isTrue) {
                        isRemovalDialogShown = true
                        errorDialog?.show()
                    }
                }
            }
        }
    }

    private fun checkCardAndShowDialog() {
        checkForCardRemove(this)
    }


    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            android.R.id.home -> {
                checkForCardRemove(this)
            }
        }
        return super.onOptionsItemSelected(item)
    }

    override fun selectBank(bank: Bank) {
        //Need to pass this in the arguments.
        if (isMoneyTransfer) {
            openActivity(AddBankAccountMoneyTransferActivity::class.java) {
                val eventProperties = HashMap<String, String>()
                eventProperties[BANK_DESTINATION] = bank.bankName
                Analytics.trackEvent(
                    CardAnalyticsConstants.EVENT_SELECT_BANK_DESTINATION,
                    eventProperties
                )
                putBundle("data", bundle)
                putParcelable(SELECTED_BANK, BankAccount(
                    bankName = bank.bankName,
                    bankCode = bank.bankCode,
                    logo = bank.logo
                ))
            }
        } else if(transactionType == TransactionType.CASH_WITHDRAWAL.type || transactionType == TransactionType.EDC_ORDER.type) {
            setResult(Activity.RESULT_OK, intent.apply {
                putExtra(SELECTED_BANK, bank)
            })
            finish()
        }
        else {
            openActivity(AddBankAccountActivity::class.java) {
                putParcelable(SELECTED_BANK, bank)
            }
        }
    }

    override fun onBackPressed() {
        trackBackPressEvent(CardAnalyticsConstants.ANDROID_BACK_BUTTON)
        handleBackPressed()
    }

    private fun trackBackPressEvent(buttonType: String) {
        val eventProperties = HashMap<String, String>()
        eventProperties[BUTTON] = buttonType
        eventProperties[TRANSACTION_TYPE] = if(isMoneyTransfer) CardAnalyticsConstants.TRANSFER else CardAnalyticsConstants.PAYMENT
        eventProperties[PAGE] = SELECT_BANK_DESTINATION
        Analytics.trackEvent(EVENT_BACK_BUTTON_CLICK, eventProperties)
    }

    private fun handleBackPressed() {
        super.onBackPressed()
    }

    override fun onCardRemove() {
        goToDestination(HomePageActivity::class.java)
    }

    override fun onDestroy() {
        super.onDestroy()
        edcCardViewModel.stopCheckCard()
    }

    private fun showWarningMessage(banks: List<Bank>) {
        val inactiveBankMessages = banks.filter {
            it.status.equals(BANK_STATUS_INACTIVE, true)
        }.map { it.metadata?.message?.messageId ?: "-" }

        if (inactiveBankMessages.size == 1) {
            binding.ebWarningInfo.setData(
                message = inactiveBankMessages[0],
                items = emptyList()
            )
            binding.ebWarningInfo.showView()
        } else if (inactiveBankMessages.size > 1) {
            val title = PaymentRemoteConfig.fetchTransferDestinationWarning()
            binding.ebWarningInfo.setData(
                message = title,
                items = inactiveBankMessages
            )
            binding.ebWarningInfo.showView()
        } else {
            binding.ebWarningInfo.hideView()
        }
    }
}