package com.bukuwarung.edc.payments.constant

import com.bukuwarung.edc.R
import com.bukuwarung.edc.payments.data.model.PaymentHistory

object PaymentConst {
    const val TYPE_PAYMENT_IN = "IN"
    const val PAYMENT_IN = "payment_in"
    const val SALDO = "SALDO"
    const val DisbursementRequest = "DisbursementRequest"
    const val CATEGORY_ID = "category_id"
    const val KYC_TIER = "kyc_tier"
    const val BANK_DOWN_TIME = "DOWN_TIME"
    const val CASH = "cash"
    const val RECORD_IN_CASH = "cash"
    const val STATUS_PENDING = "PENDING"
    const val STATUS_COMPLETED = "COMPLETED"
    const val STATUS_PAID = "PAID"
    const val STATUS_FAILED = "FAILED"
    const val STATUS_HOLD = "HOLD"
    const val STATUS_CANCELLED = "CANCELLED"
    const val STATUS_REFUNDING = "REFUNDING"
    const val STATUS_REFUNDED = "REFUNDED"
    const val STATUS_REFUNDING_FAILED = "REFUNDING_FAILED"
    const val TYPE_PAYMENT_OUT = "OUT"
    const val TYPE_SALDO_IN = "SALDO"
    const val TYPE_SALDO_REFUND = "SALDO_IN"
    const val TYPE_SALDO_OUT = "SALDO_OUT"
    const val TYPE_SALDO_CASHBACK = "SALDO_CASHBACK"
    const val TYPE_CASHBACK_IN = "CASHBACK_IN"
    const val TYPE_CASHBACK_OUT = "CASHBACK_OUT"
    const val BNPL = "BNPL"
    const val SALDO_FREEZE = "SALDO_FREEZE"
    const val DAILY_LIMIT_REACHED = "DAILY_LIMIT_REACHED"
    const val MONTHLY_LIMIT_REACHED = "MONTHLY_LIMIT_REACHED"
    const val SALDO_RECHARGE = "SALDO_RECHARGE"

    const val TYPE_SAVINGS = "SAVINGS"
    const val TYPE_CHECKING = "CHECKING"

    const val TRANSACTION_SUCCESS = "SUCCESS"
    const val TRANSACTION_PENDING = "PENDING"

    const val CHECKOUT_TOKEN= "CHECKOUT_TOKEN"
    const val HAS_PAYMENT_PIN = "HAS_PAYMENT_PIN"

    const val TYPE_PAYMENT_PIN_CHANGE = "PAYMENT_PIN_CHANGE"
    const val TYPE_PAYMENT_PIN_SET = "PAYMENT_PIN_SET"
    const val OTP_WAIT_TIME_IN_SECONDS = 25

    const val clickDebounceTime = 500L
    const val DEFAULT_FILTER_CALENDAR_MAX_RANGE = 31
    const val TYPE_DIGITAL_PRODUCT = "DIGITAL_PRODUK"
    const val TYPE_PEMBAYARAN = "PEMBAYARAN"
    const val TYPE_SALDO_ALL = "SALDO_ALL"
    const val TYPE_CASHBACK_ALL = "CASHBACK_ALL"
    const val TYPE_PAY_IN = "IN"
    const val TYPE_PAY_OUT = "OUT"
    const val TYPE_CASHBACK = "SALDO_REDEMPTION"
    const val TYPE_DEFAULT = "DEFAULT"
    const val DEFAULT_WITHOUT_PPOB = "DEFAULT_WITHOUT_PPOB"
    const val CATEGORY_TRAIN_TICKET = "TRAIN_TICKET"

    const val TRX_CODE_VIEW_TAG = "trx_code"
    const val BILL_AMOUNT_VIEW_TAG = "bill_amount"
    const val SERVICE_FEE_VIEW_TAG = "service_fee"
    const val TOTAL_PAYMENT_VIEW_TAG = "total_payment"
    const val PAYMENT_AMOUNT_VIEW_TAG = "payment_amount"
    const val SERVICE_FEE_DIVIDER_VIEW_TAG = "service_fee_divider"

    const val CATEGORY_PULSA = "PULSA"
    const val CATEGORY_LISTRIK = "LISTRIK" // To be used for category for Prepaid listrik
    const val CATEGORY_PLN_POSTPAID = "PLN_POSTPAID"
    const val CATEGORY_LISTRIK_POSTPAID = "LISTRIK_POSTPAID" // To be used for category for Postpaid listrik
    const val CATEGORY_EWALLET = "EMONEY"
    const val CATEGORY_PAKET_DATA = "PAKET_DATA"
    const val CATEGORY_PULSA_POSTPAID = "PASCABAYAR"
    const val CATEGORY_VOUCHER_GAME = "VOUCHER_GAME"
    const val CATEGORY_BPJS = "BPJS"
    const val CATEGORY_PDAM = "PDAM"
    const val CATEGORY_MULTIFINANCE = "ANGSURAN"
    const val CATEGORY_INTERNET_DAN_TV_CABLE = "INTERNET_DAN_TV_KABEL"
    const val CATEGORY_VEHICLE_TAX = "VEHICLE_TAX"

    const val SALDO_LOGO = "https://bw-assets.s3-ap-southeast-1.amazonaws.com/payments/bank-logos/saldo.png"

    enum class HISTORY_TABS {
        ALL, PPOB, PEMBAYARAN, SALDO
    }
    enum class DATE_PRESET {
        TODAY, YESTERDAY, THIS_WEEK, LAST_WEEK, THIS_MONTH, LAST_MONTH, THIS_YEAR, LAST_YEAR,
        LAST_SEVEN_DAYS, LAST_TEN_DAYS, ALL, CUSTOM_RANGE
    }

    val TYPE_PEMBAYARAN_CHILDREN = arrayListOf(TYPE_PAY_IN, TYPE_PAY_OUT)
    val TYPE_SALDO_CHILDREN = arrayListOf(TYPE_SALDO_IN, TYPE_SALDO_OUT, PaymentHistory.TYPE_SALDO_BNPL)
    val TYPE_CASHBACK_CHILDREN = arrayListOf(TYPE_CASHBACK)
    const val TAG_PRICE_LABEL = "TAG_PRICE_LABEL"
    const val TAG_BUKU_ORIGIN = "TAG_BUKU_ORIGIN"
    const val TAG_HISTORY_DETAIL = "TAG_HISTORY_DETAIL"

    val CATEGORY_NAME_MAP = HashMap<String, Int>().apply {
        put(CATEGORY_PULSA, R.string.pulsa)
        put(CATEGORY_LISTRIK, R.string.token_listrik)
        put(CATEGORY_EWALLET, R.string.ewallet)
        put(CATEGORY_PAKET_DATA, R.string.packet_data)
        put(CATEGORY_PLN_POSTPAID, R.string.listrik_postpaid)
        put(CATEGORY_LISTRIK_POSTPAID, R.string.listrik_postpaid)
        put(CATEGORY_PULSA_POSTPAID, R.string.tagihan_pascabayar_capital)
        put(CATEGORY_VOUCHER_GAME, R.string.voucher_game)
        put(CATEGORY_BPJS, R.string.bpjs)
        put(CATEGORY_PDAM, R.string.pdam)
        put(CATEGORY_MULTIFINANCE, R.string.multifinance)
        put(CATEGORY_INTERNET_DAN_TV_CABLE, R.string.internet_dan_tv_cable)
        put(CATEGORY_VEHICLE_TAX, R.string.vehicle_tax)
        put(CATEGORY_TRAIN_TICKET, R.string.train)
    }

    val CATEGORY_EXPIRED_ICON_CIRCLE_MAP = HashMap<String, Int>().apply {
        put(CATEGORY_PULSA, R.drawable.vector_pulsa_circle_grey_bg)
        put(CATEGORY_LISTRIK, R.drawable.vector_listrik_circle_grey_bg)
        put(CATEGORY_EWALLET, R.drawable.vector_ewallet_circle_grey_bg)
        put(CATEGORY_PAKET_DATA, R.drawable.vector_packet_data_circle_grey_bg)
        put(CATEGORY_PLN_POSTPAID, R.drawable.vector_listrik_postpaid_circle_grey_bg)
        put(CATEGORY_LISTRIK_POSTPAID, R.drawable.vector_listrik_postpaid_circle_grey_bg)
        put(CATEGORY_PULSA_POSTPAID, R.drawable.vector_pulsa_postpaid_circle_grey_bg)
        put(CATEGORY_VOUCHER_GAME, R.drawable.vector_voucher_game_circle_grey_bg)
        put(CATEGORY_BPJS, R.drawable.vector_bpjs_circle_grey_bg)
        put(CATEGORY_PDAM, R.drawable.vector_pdam_circle_grey_bg)
        put(CATEGORY_MULTIFINANCE, R.drawable.vector_multifinance_circle_grey_bg)
        put(CATEGORY_INTERNET_DAN_TV_CABLE, R.drawable.vector_internet_dan_tv_cable_circle_grey_bg)
        put(CATEGORY_VEHICLE_TAX, R.drawable.vector_vehicle_tax_circle_grey_bg)
        put(CATEGORY_TRAIN_TICKET, R.drawable.vector_train_grey_circle_bg)
    }

    val CATEGORY_ICON_CIRCLE_MAP = HashMap<String, Int>().apply {
        put(CATEGORY_PULSA, R.drawable.vector_pulsa_circle_bg)
        put(CATEGORY_LISTRIK, R.drawable.vector_listrik_circle_bg)
        put(CATEGORY_EWALLET, R.drawable.vector_ewallet_circle_bg)
        put(CATEGORY_PAKET_DATA, R.drawable.vector_paket_data_circle_bg)
        put(CATEGORY_PLN_POSTPAID, R.drawable.vector_listrik_postpaid_circle_bg)
        put(CATEGORY_LISTRIK_POSTPAID, R.drawable.vector_listrik_postpaid_circle_bg)
        put(CATEGORY_PULSA_POSTPAID, R.drawable.vector_pulsa_postpaid_circle_bg)
        put(CATEGORY_VOUCHER_GAME, R.drawable.vector_voucher_game_circle_bg)
        put(CATEGORY_BPJS, R.drawable.vector_bpjs_circle_bg)
        put(CATEGORY_PDAM, R.drawable.vector_pdam_circle_bg)
        put(CATEGORY_MULTIFINANCE, R.drawable.vector_multifinance_circle_bg)
        put(CATEGORY_INTERNET_DAN_TV_CABLE, R.drawable.vector_internet_dan_tv_cable_circle_bg)
        put(CATEGORY_VEHICLE_TAX, R.drawable.vector_vehicle_tax_circle_bg)
        put(CATEGORY_TRAIN_TICKET, R.drawable.vector_train_circle_bg)
    }

    enum class BankAccountOwner {
        SELF, BUSINESS_SELF, BUSINESS_PARTNER, RELATIVE, OTHERS
    }

}