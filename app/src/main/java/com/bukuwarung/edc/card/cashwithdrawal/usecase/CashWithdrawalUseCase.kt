package com.bukuwarung.edc.card.cashwithdrawal.usecase

import com.bukuwarung.edc.card.cashwithdrawal.model.SettlementBankAccount
import com.bukuwarung.edc.card.cashwithdrawal.model.SettlementBankAccountResponse
import com.bukuwarung.edc.card.cashwithdrawal.repository.CashWithdrawalRepo
import com.bukuwarung.edc.payments.data.model.BankAccount
import retrofit2.Response
import javax.inject.Inject

class CashWithdrawalUseCase @Inject constructor(private val repo: CashWithdrawalRepo) {
    suspend fun addSettlementBankAccount(
        paymentAccountId: String,
        terminalId: String,
        bankAccount: SettlementBankAccount
    ): Response<SettlementBankAccount> {
        return repo.addSettlementBankAccount(paymentAccountId, terminalId, bankAccount)
    }

    suspend fun getSettlementBankList(
        terminalId: String,
        isPrimary: Boolean = true
    ): Response<SettlementBankAccountResponse> {
        return repo.getSettlementBankList(terminalId, isPrimary)
    }


    suspend fun setPrimarySettlementBankAccount(
        bankAccountId: String
    ): Response<Unit> {
        return repo.setPrimarySettlementBankAccount(bankAccountId)
    }

    suspend fun deleteSettlementBankAccount(
        bankAccountId: String
    ): Response<Unit> {
        return repo.deleteSettlementBankAccount(bankAccountId)
    }
}