package com.bukuwarung.edc.card.cashwithdrawal.repository

import com.bukuwarung.edc.card.cashwithdrawal.api.CashWithdrawalApi
import com.bukuwarung.edc.card.cashwithdrawal.model.SettlementBankAccount
import com.bukuwarung.edc.card.cashwithdrawal.model.SettlementBankAccountResponse
import com.bukuwarung.edc.payments.data.model.BankAccount
import retrofit2.Response
import javax.inject.Inject

class CashWithdrawalRepo @Inject constructor(private val api: CashWithdrawalApi) {

    suspend fun addSettlementBankAccount(
        paymentAccountId: String,
        terminalId: String,
        bankAccount: SettlementBankAccount
    ): Response<SettlementBankAccount> {
        return api.addSettlementBankAccount(paymentAccountId, terminalId, bankAccount)
    }

    suspend fun getSettlementBankList(
        terminalId: String,
        isPrimary: Boolean
    ): Response<SettlementBankAccountResponse> {
        return api.getSettlementBankList(terminalId, isPrimary)
    }

    suspend fun setPrimarySettlementBankAccount(
        bankAccountId: String
    ): Response<Unit> {
        return api.setPrimarySettlementBankAccount(bankAccountId)
    }

    suspend fun deleteSettlementBankAccount(
        bankAccountId: String
    ): Response<Unit> {
        return api.deleteSettlementBankAccount(bankAccountId)
    }
}