package com.bukuwarung.edc.card.ui.receipt

import android.graphics.Bitmap
import android.graphics.Canvas
import android.view.View
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.toBitmap
import com.bukuwarung.bluetooth_printer.activities.print.setup.SetupBluetoothDeviceActivity
import com.bukuwarung.edc.BuildConfig
import com.bukuwarung.edc.R
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants
import com.bukuwarung.edc.card.constant.PrintConst.ALERT_INFO
import com.bukuwarung.edc.card.constant.ReceiptType
import com.bukuwarung.edc.card.data.model.CardReceiptResponse
import com.bukuwarung.edc.card.data.model.EdcOrderWarrantyNudgeBody
import com.bukuwarung.edc.card.data.model.EndUserStatusValues
import com.bukuwarung.edc.card.domain.model.consts.Constants.CARD_ENTRY_MODE_IC
import com.bukuwarung.edc.card.transfermoney.constants.CheckBalancePinConfig.EDC_ORDER_WARRANTY_NUDGE
import com.bukuwarung.edc.card.ui.ErrorMapping
import com.bukuwarung.edc.databinding.ActivityCardReceiptBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.Constant.TAG_PRINT
import com.bukuwarung.edc.global.base.BaseCardActivity
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.payments.data.model.CardErrorType
import com.bukuwarung.edc.payments.ui.dialog.CardErrorDialog
import com.bukuwarung.edc.printer.ui.dialog.BukuDialog
import com.bukuwarung.edc.util.ImageUtils
import com.bukuwarung.edc.util.ToastUtil.setToast
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.addQuery
import com.bukuwarung.edc.util.Utils.bwLog
import com.bukuwarung.edc.util.asVisibility
import com.bukuwarung.edc.util.get
import com.bukuwarung.edc.util.getDrawableCompat
import com.bukuwarung.edc.util.goToDestination
import com.bukuwarung.edc.util.hideView
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.showView
import com.bukuwarung.edc.util.singleClick
import com.bukuwarung.edc.webview.WebviewActivity
import com.bukuwarung.edc.webview.constant.ClassConstants
import com.bukuwarung.edc.webview.constant.ClassConstants.WEBVIEW_URL
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import dagger.hilt.android.AndroidEntryPoint
import java.io.ByteArrayOutputStream
import kotlin.collections.set

@AndroidEntryPoint
class CardReceiptActivity : BaseCardActivity() {

    companion object {
        const val API_RESPONSE = "api_response"
        const val DATA = "data"
        const val PAN = "PAN"
        const val ACCOUNT_TYPE = "account_type"
        const val TRANSACTION_TYPE = "transaction_type"
        const val NOTES = "notes"
        const val _68 = "68"
    }

    private val viewModel: CardReceiptViewModel by viewModels()

    private lateinit var binding: ActivityCardReceiptBinding
    private val cardReceiptResponse by lazy { intent?.getParcelableExtra(API_RESPONSE) as? CardReceiptResponse? }
    private val bundle by lazy { intent?.getBundleExtra(DATA) }
    private val pan by lazy { bundle?.getString(PAN).orEmpty() }
    private val accountType by lazy { bundle?.getString(ACCOUNT_TYPE).orEmpty() }
    private val transactionType by lazy { bundle?.getInt(TRANSACTION_TYPE, 1) }
    private val notes by lazy { bundle?.getString(NOTES).orEmpty() }
    private val cardEntryMode by lazy { bundle?.getInt("CARD_ENTRY_MODE") }
    private val warrantyNudge by lazy {
        Utils.sharedPreferences.get(EDC_ORDER_WARRANTY_NUDGE,"")
    }
    private var isPrintingCompleted = false
    private var dialog: BukuDialog? = null

    override fun setViewBinding() {
        binding = ActivityCardReceiptBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        Utils.clearIncompleteTransaction()
        onBackPressedDispatcher.addCallback(
            this,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    backPressFn(true)
                }
            }
        )

        with(binding.ivClose) {
            setOnClickListener { backPressFn(false) }
            visibility = Utils.isCardReader().asVisibility()
        }
        with(binding.tvClose) {
            setOnClickListener { backPressFn(false) }
            visibility = Utils.isCardReader().asVisibility()
        }
        renderStatus()
        viewModel.init(cardReceiptResponse, pan, transactionType, notes, accountType, cardEntryMode?:CARD_ENTRY_MODE_IC)
        renderInvoice()
        getWarrantyNudge()
    }

    private fun renderStatus() = with(binding) {
        when {
            cardReceiptResponse?.amount != null -> { //case TF or CW
                tvTotalPayment.hideView()
                val endUserStatus = cardReceiptResponse?.endUserStatus
                val transactionType = cardReceiptResponse?.transactionType
                when (endUserStatus) {
                    EndUserStatusValues.SUCCESS -> {
                        ivStatus.setImageDrawable(getDrawableCompat(R.mipmap.ic_blue_tick))
                        cardReceiptResponse?.transactionType?.let {
                            if (it == TransactionType.CASH_WITHDRAWAL.type) {
                                tvPaymentStatus.text = getString(R.string.successful_status_cash_withdrawal)
                            } else {
                                tvPaymentStatus.text = getString(R.string.successful_status_transfer)
                            }
                        }
                    }

                    EndUserStatusValues.PENDING_SETTLEMENT ->{
                        ivStatus.setImageDrawable(getDrawableCompat(R.mipmap.ic_blue_tick))
                        tvPaymentStatus.text = getString(R.string.success_label)
                        info.root.showView()
                        info.tvInfoText.text = "Pencairan uang diproses. Jangan khawatir, transaksi akan sukses."
                    }

                    EndUserStatusValues.PENDING_REFRESH -> {
                        ivStatus.setImageDrawable(getDrawableCompat(R.drawable.ic_hourglass))
                        tvPaymentStatus.text = "Transaksi Pending"
                        info.root.showView()
                        when (transactionType) {
                            "POSTING",
                            TransactionType.TRANSFER_POSTING.type -> {
                                info.tvInfoText.text = "Transaksi Pending. Cek berkala untuk mengetahui status akhir transaksi."
                            }
                            TransactionType.CASH_WITHDRAWAL.type,
                            TransactionType.CASH_WITHDRAWAL_POSTING.type -> {
                                info.tvInfoText.text = "Transaksi Pending. Jangan menyerahkan uang terlebih dahulu jika Anda sedang melayani transaksi tarik tunai."
                            }
                            else -> {
                                info.root.hideView()
                            }
                        }
                    }
                    EndUserStatusValues.PENDING -> {
                        ivStatus.setImageDrawable(getDrawableCompat(R.drawable.ic_hourglass))
                        tvPaymentStatus.text = "Transaksi Pending"
                        info.root.showView()
                        when (transactionType) {
                            "POSTING",
                            TransactionType.TRANSFER_POSTING.type -> {
                                info.tvInfoText.text = "Transaksi Pending. Cek berkala untuk mengetahui status akhir transaksi."
                            }
                            TransactionType.CASH_WITHDRAWAL.type,
                            TransactionType.CASH_WITHDRAWAL_POSTING.type-> {
                                info.tvInfoText.text = "Transaksi Pending. Jangan menyerahkan uang terlebih dahulu jika Anda sedang melayani transaksi tarik tunai."
                            }
                            else -> {
                                info.root.hideView()
                            }
                        }
                    }
                    else -> tvPaymentStatus.text = getString(R.string.failed_status_transfer)
                }
            }
            else -> {
                ivStatus.setImageDrawable(getDrawableCompat(R.mipmap.ic_blue_tick))
                tvPaymentStatus.text = getString(R.string.balance_check_successful)
            }
        }
        //cardReceiptResponse?.amount will be picked in case of transfer money and cardReceiptResponse?.balanceInformation?.balance?.toDouble() will be picked up in the check balance money flow.
        val amount = cardReceiptResponse?.amount ?: cardReceiptResponse?.balanceInformation?.balance?.toDouble()
        tvTotalPaymentAmount.text = Utils.formatAmount(amount)
    }

    private fun renderInvoice() = with(binding.includeOrderInvoice){
        val drawable = ContextCompat.getDrawable(this@CardReceiptActivity, R.drawable.bersama_logo1)
        val bitmapHeader = drawable?.toBitmap()

        var baos: ByteArrayOutputStream? = null
        baos = ByteArrayOutputStream()
        bitmapHeader?.compress(Bitmap.CompressFormat.PNG, 100, baos)
        val headerImage = baos.toByteArray()

        val drawableAgen = ContextCompat.getDrawable(this@CardReceiptActivity, R.drawable.buku_agent_print_1)
        var bitmapAgen = drawableAgen?.toBitmap()
        var baosAgen: ByteArrayOutputStream? = null
        baosAgen = ByteArrayOutputStream()
        bitmapAgen = bitmapAgen?.let { Bitmap.createScaledBitmap(it, 120, 72, true) }
        bitmapAgen?.compress(Bitmap.CompressFormat.PNG, 100, baosAgen)
        val bukuAgenLogo = baosAgen.toByteArray()

        val maskedCard = Utils.maskCardNo(pan)
        orderInvoice.apply {
            makeCardInvoice(
                cardReceiptResponse,
                maskedCard,
                accountType,
                notes,
                cardEntryMode ?: CARD_ENTRY_MODE_IC
            )
            this.showView()
        }
        tvAutoPrint.showView()
        tvInvoiceProof.hideView()
        if (Utils.hasPairedPrinter()) {
            viewModel.onEventReceived(
                CardReceiptViewModel.Event.StartAutoPrintTimer(
                    60000
                )
            )
        }
        btnPrint.text = getString(R.string.print_receipt)
        btnPrint.singleClick {
            bwLog("CardReceiptActivity","btnPrint.singleClick")
            if (Utils.hasPairedPrinter()) {
                val map = HashMap<String, String>()
                Analytics.trackEvent(CardAnalyticsConstants.EVENT_INFO_CARD_CONFIRM, map)
                viewModel.onEventReceived(
                    CardReceiptViewModel.Event.OnPrintButtonClicked(
                        if (Utils.isCardReader()) ReceiptType.RECEIPT_TYPE_MERCHANT else viewModel.nextPendingCopy,
                        CardReceiptViewModel.Destination.TRANSACTION_FLOW,
                        headerImage,
                        bukuAgenLogo,
                        bitmapHeader,
                        bitmapAgen
                    )
                )
                isPrintingCompleted = true;
            } else {
                showPrinterConnectionDialog()
            }
        }

        btnShare.visibility = Utils.isCardReader().asVisibility()
        btnShare.setOnClickListener {
            val bitmap = getBitmapFromView(binding.includeOrderInvoice.orderInvoice)
            bitmap?.let {
                if (ImageUtils.isAppInstalled(this@CardReceiptActivity, "com.whatsapp")) {
                    ImageUtils.shareImage(this@CardReceiptActivity, it, Utils.getBusinessName())
                } else {
                    Toast.makeText(this@CardReceiptActivity, "WhatsApp is not installed on this device", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun getBitmapFromView(view: View): Bitmap? {
        val totalHeight = view.height
        val totalWidth = view.width

        val bitmap = Bitmap.createBitmap(totalWidth, totalHeight, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        view.draw(canvas)

        ImageUtils.addWatermark(canvas, totalWidth, totalHeight)

        return bitmap
    }

    override fun subscribeState() {
        val errorDialog: CardErrorDialog? = CardErrorDialog(
            context = this,
            errorType = CardErrorType.REMOVE_CARD,
            errorCode = ErrorMapping.removeCardErrorCode[0]
        )
        viewModel.state.observe(this) {
            val drawable = ContextCompat.getDrawable(this, R.drawable.bersama_logo1)
            val bitmapHeader = drawable?.toBitmap()

            var baos: ByteArrayOutputStream? = null
            baos = ByteArrayOutputStream()
            bitmapHeader?.compress(Bitmap.CompressFormat.PNG, 100, baos)
            val headerImage = baos.toByteArray()

            val drawableAgen = ContextCompat.getDrawable(this, R.drawable.buku_agent_print_1)
            var bitmapAgen = drawableAgen?.toBitmap()
            var baosAgen: ByteArrayOutputStream? = null
            baosAgen = ByteArrayOutputStream()
            bitmapAgen = bitmapAgen?.let { Bitmap.createScaledBitmap(it, 120, 72, true) }
            bitmapAgen?.compress(Bitmap.CompressFormat.PNG, 100, baosAgen)
            val bukuAgenLogo = baosAgen.toByteArray()


            when (it) {
                is CardReceiptViewModel.State.SetPrintStart -> {
                    bwLog(TAG_PRINT, "Print start: ${it.printState.receiptType}")
                    dialog = BukuDialog(
                        context = this@CardReceiptActivity,
                        title = getString(it.printState.messageTitle),
                        subTitle = getString(it.printState.messageBody),
                        image = R.drawable.ic_hourglass,
                        isLoader = true,
                        btnLeftListener = {
                            dialog?.dismiss()
                        },
                        btnRightListener = {
                        },
                        btnLeftText = getString(R.string.batal),
                        btnRightText = getString(R.string.print)
                    )
                    dialog?.show()
                }

                is CardReceiptViewModel.State.SetPrintComplete -> {
                    bwLog(TAG_PRINT, "Print complete: ${it.printState.receiptType}")
                    dialog?.dismiss()
                }

                is CardReceiptViewModel.State.SetPrintError -> {
                    setValuesOnFailurePrinting()
                    bwLog(
                        TAG_PRINT,
                        "Print error [${it.errorStatus.errorCode}]: ${it.errorStatus.msg}"
                    )
                    dialog?.dismiss()
                    setToast(
                        this,
                        it.errorStatus.errorLevel,
                        it.errorStatus.msg,
                        this.findViewById(R.id.iv_close)
                    )
                    binding.ivClose.showView()
                    binding.tvClose.showView()
                    isPrintingCompleted = true
                }

                is CardReceiptViewModel.State.SetAutoPrintStart -> {
                    bwLog(TAG_PRINT, "Auto print start: ${it.receiptType}")
                    if(Utils.hasPairedPrinter()) {
                        viewModel.onEventReceived(
                            CardReceiptViewModel.Event.OnAutoPrint(
                                it.receiptType,
                                CardReceiptViewModel.Destination.TRANSACTION_FLOW,
                                headerImage,
                                bukuAgenLogo,
                                bitmapHeader,
                                bitmapAgen
                            )
                        )
                    }else{
                        showPrinterConnectionDialog()
                    }
                }

                is CardReceiptViewModel.State.SetPrintSuccess -> {
                    bwLog(TAG_PRINT, "Confirm printing : ${it.printState.receiptType}")
                    dialog?.dismiss()
                    dialog = BukuDialog(
                        context = this@CardReceiptActivity,
                        title = getString(it.printState.messageTitle),
                        subTitle = getString(it.printState.messageBody),
                        image = it.printState.stateImage,
                        isLoader = it.printState.isLoader,
                        btnLeftListener = {
                            finishPrinting()
                        },
                        btnRightListener = {
                            if (!it.printState.isLoader) {
                                dialog?.dismiss()
                                viewModel.onEventReceived(
                                    CardReceiptViewModel.Event.OnPrintButtonClicked(
                                        viewModel.nextPendingCopy,
                                        CardReceiptViewModel.Destination.TRANSACTION_FLOW,
                                        headerImage,
                                        bukuAgenLogo,
                                        bitmapHeader,
                                        bitmapAgen
                                    )
                                )
                            }
                        },
                        btnLeftText = getString(R.string.batal),
                        btnRightText = getString(R.string.print)
                    )
                    dialog?.show()
                }

                is CardReceiptViewModel.State.SetPrintFinish -> {
                    dialog?.dismiss()
                    setToast(this,
                        ALERT_INFO,
                        getString(R.string.print_finish),
                        this.findViewById(R.id.iv_close)
                    )
                    setValuesOnSuccessfulPrinting()
                    bwLog(TAG_PRINT, "Print complete remove card")
                    finishPrinting()
                }
            }
        }
    }

    private fun finishPrinting(){
        setToast(this,ALERT_INFO,getString(R.string.print_finish), this.findViewById(R.id.iv_close))
        setValuesOnSuccessfulPrinting()
        if(viewModel.nextPendingCopy == ReceiptType.RECEIPT_TYPE_NONE) {
            startListeningForCard()
        }
        bwLog(TAG_PRINT, "Print complete remove card")
    }

    private fun backPressFn(isFromBackButton: Boolean) {
        if (isPrintingCompleted || Utils.isCardReader()) {
            val map = HashMap<String, String>()
            map[CardAnalyticsConstants.BUTTON] =
                if (isFromBackButton) CardAnalyticsConstants.ANDROID_BACK_BUTTON else CardAnalyticsConstants.TOP_ARROW_BACK_BUTTON
            map[CardAnalyticsConstants.TRANSACTION_TYPE] =
                if (cardReceiptResponse?.balanceInformation != null) CardAnalyticsConstants.BALANCE_CHECK else CardAnalyticsConstants.TRANSFER
            map[CardAnalyticsConstants.PAGE] =
                if (cardReceiptResponse?.balanceInformation != null) CardAnalyticsConstants.CHECK_BALANCE_DETAIL else CardAnalyticsConstants.TRANSFER_DETAIL
            Analytics.trackEvent(CardAnalyticsConstants.EVENT_BACK_BUTTON_CLICK, map)
            startListeningForCard()
        }
    }


    private fun setValuesOnSuccessfulPrinting() {
        binding.ivClose.showView()
        binding.tvClose.showView()
        isPrintingCompleted = true
    }

    private fun setValuesOnFailurePrinting() {
        binding.ivClose.showView()
        binding.tvClose.showView()
        isPrintingCompleted = false
    }



    private fun startListeningForCard() {
        checkForCardRemove(this)
    }

    override fun onCardRemove() {
        goToDestination(HomePageActivity::class.java)
    }

    private fun showPrinterConnectionDialog() {
        dialog?.dismiss()
        dialog = BukuDialog(
            context = this,
            title = "Printer Belum Terhubung",
            subTitle = "Silakan sambungkan aplikasi ke printer Anda untuk lanjut mencetak struk transaksi.",
            image = R.drawable.ic_printing,
            isLoader = false,
            btnLeftListener = {
                dialog?.dismiss()
                setValuesOnSuccessfulPrinting()
            },
            btnRightListener = {
                dialog?.dismiss()
                openActivity(SetupBluetoothDeviceActivity::class.java){
                    putString(
                        SetupBluetoothDeviceActivity.DEVICE_TYPE,
                        SetupBluetoothDeviceActivity.PRINTER)
                }
            },
            btnLeftText = getString(R.string.later),
            btnRightText = "Hubungkan"
        )
        dialog?.show()
    }

    private fun getWarrantyNudge() {
        val typeToken = object : TypeToken<EdcOrderWarrantyNudgeBody>() {}.type
        val edcWarrantyNudge = Gson().fromJson<EdcOrderWarrantyNudgeBody>(warrantyNudge, typeToken)
        binding.warrantyLayout.root.visibility =
            (edcWarrantyNudge.isVisible == true && Utils.isCardReader() && Utils.hasAMoreFunSakuDevice()).asVisibility()
        val orderChannel = if (Utils.isAtmPro()) "MiniATMPro" else "BUKUAGEN"
        binding.warrantyLayout.btnRedirect.singleClick {
            openActivity(WebviewActivity::class.java) {
                putString(
                    WEBVIEW_URL,
                    BuildConfig.API_BASE_URL + edcWarrantyNudge.redirectionUrl?.addQuery("order_channel=$orderChannel")
                )
                putBoolean(ClassConstants.HIDE_TOOLBAR, true)
            }
        }
        binding.warrantyLayout.ivClose.singleClick {
            binding.warrantyLayout.root.hideView()
        }
    }

    override fun onDestroy() {
        bwLog(e = Exception("CardReceiptActivity-onDestroy"))
        super.onDestroy()
    }

}