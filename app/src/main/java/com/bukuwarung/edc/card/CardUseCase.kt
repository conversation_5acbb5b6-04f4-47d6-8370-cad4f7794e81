package com.bukuwarung.edc.card

import com.bukuwarung.edc.card.data.repository.CheckBalanceRepository
import com.bukuwarung.edc.card.data.model.CheckBalanceRequest
import com.bukuwarung.edc.card.transfermoney.data.repository.TransferMoneyRepository
import com.bukuwarung.edc.card.transfermoney.model.TransferMoneyRequestResponseBody
import javax.inject.Inject

class CardUseCase @Inject constructor(
    private val checkBalanceRepository: CheckBalanceRepository,
    private val transferMoneyRepository: TransferMoneyRepository
) {

    suspend fun fetchCardBalance(accountId: String, checkBalanceRequest: CheckBalanceRequest) =
        checkBalanceRepository.fetchCardBalance(accountId, checkBalanceRequest)

    suspend fun inquireTransferMoney(
        accountId: String,
        requestBody: TransferMoneyRequestResponseBody
    ) =
        transferMoneyRepository.enquireTransferMoney(accountId, requestBody)

    suspend fun cashWithdrawalInquiry(
        accountId: String,
        requestBody: TransferMoneyRequestResponseBody
    ) =
        transferMoneyRepository.cashWithdrawalInquiry(accountId, requestBody)

    suspend fun transferMoney(accountId: String, requestBody: TransferMoneyRequestResponseBody) =
        transferMoneyRepository.transferMoney(accountId, requestBody)
}