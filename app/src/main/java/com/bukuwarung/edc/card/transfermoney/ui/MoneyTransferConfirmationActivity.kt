package com.bukuwarung.edc.card.transfermoney.ui

import android.os.Bundle
import com.bukuwarung.bluetooth_printer.utils.hideView
import com.bukuwarung.edc.R
import com.bukuwarung.edc.card.CardPinDynamicActivity
import com.bukuwarung.edc.card.ExternalPinpadActivity
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants.ACCOUNT_DESTINATION
import com.bukuwarung.edc.card.constant.CardAnalyticsConstants.BANK_DESTINATION
import com.bukuwarung.edc.card.domain.model.consts.Constants
import com.bukuwarung.edc.card.transfermoney.model.TransferMoneyData
import com.bukuwarung.edc.card.ui.EdcCardViewModel
import com.bukuwarung.edc.databinding.ActivityPaymentConfirmationBinding
import com.bukuwarung.edc.global.Analytics
import com.bukuwarung.edc.global.Constant
import com.bukuwarung.edc.global.base.BaseCardActivity
import com.bukuwarung.edc.global.enums.TransactionType
import com.bukuwarung.edc.homepage.ui.home.HomePageActivity
import com.bukuwarung.edc.payments.data.model.BankAccount
import com.bukuwarung.edc.payments.ui.addbank.SelectBankActivity.Companion.IS_MONEY_TRANSFER
import com.bukuwarung.edc.util.Utils
import com.bukuwarung.edc.util.goToDestination
import com.bukuwarung.edc.util.openActivity
import com.bukuwarung.edc.util.showView
import dagger.hilt.android.AndroidEntryPoint
import kotlin.collections.HashMap
import kotlin.collections.set

@AndroidEntryPoint
class MoneyTransferConfirmationActivity :
    BaseCardActivity() {
    private lateinit var binding: ActivityPaymentConfirmationBinding

    private var bundle: Bundle? = null

    private val bankAndAmountDetails by lazy { intent?.getParcelableExtra("BANK_AMOUNT_DETAILS") as? TransferMoneyData }
    private var transactionType = TransactionType.TRANSFER_INQUIRY.type
    private val isMoneyTransfer = true

    override fun setViewBinding() {
        binding = ActivityPaymentConfirmationBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        binding.toolbar.setNavigationIcon(R.drawable.ic_back)
        binding.toolbar.setNavigationOnClickListener {
            trackBackPressEvent(CardAnalyticsConstants.TOP_ARROW_BACK_BUTTON)
            onBackPressed()
        }
        binding.includePaymentMethod.root.hideView()
        binding.tvWarning.hideView()
        binding.llNext.showView()
        if (intent.hasExtra("data")) {
            bundle = intent.getBundleExtra("data")
            transactionType = bundle!!.getString(Constant.INTENT_KEY_TRANSACTION_TYPE)!!
        }
        val bankAccount = BankAccount(
            bankCode = bankAndAmountDetails?.bank?.bankCode,
            bankName = bankAndAmountDetails?.bank?.bankName,
            logo = bankAndAmountDetails?.bank?.logo,
            accountNumber = bankAndAmountDetails?.accountNumber,
            accountHolderName = bankAndAmountDetails?.accountHolderName
        )
        if(transactionType == TransactionType.CASH_WITHDRAWAL.type){
            binding.toolbar.title = getString(R.string.cash_withdrawal_amount)
            binding.etInputNote.hideView()
            binding.tilLayoutInputNote.hideView()
            binding.pdvView.setCashWithdrawalUiChanges(
                bankAndAmountDetails?.amount?.toDouble(),
                bankAccount
            )
        }else{
            binding.toolbar.title = getString(R.string.destination_account)
            binding.etInputNote.showView()
            binding.tilLayoutInputNote.showView()
            binding.pdvView.setMoneyTransferUiChanges(
                bankAndAmountDetails?.optionalText,
                bankAndAmountDetails?.amount?.toDouble(),
                bankAccount
            )
        }
        binding.tilLayoutInputNote.hideView()
        binding.btnNext.setOnClickListener {
            val eventProperties = HashMap<String, String>()
            eventProperties[BANK_DESTINATION] =
                bankAndAmountDetails?.bank?.bankName.orEmpty()
            eventProperties[ACCOUNT_DESTINATION] =
                bankAndAmountDetails?.accountNumber.orEmpty()
            eventProperties[CardAnalyticsConstants.AMOUNT] =
                bankAndAmountDetails?.amount?.toString().orEmpty()
            eventProperties[CardAnalyticsConstants.NOTE] =
                bankAndAmountDetails?.optionalText.orEmpty()
            eventProperties[CardAnalyticsConstants.TRANSACTION_TYPE] = Utils.getTransactionTypeAnalytics(
                transactionType
            )
            Analytics.trackEvent(
                CardAnalyticsConstants.EVENT_TRANSFER_DETAIL_CLICK,
                eventProperties
            )
            openActivity(Utils.getCardPinActivity(bundle?.getString(CardPinDynamicActivity.PAN).orEmpty())) {
                putBundle("data", bundle)
                putParcelable("BANK_AMOUNT_DETAILS", bankAndAmountDetails)
                putBoolean(IS_MONEY_TRANSFER, isMoneyTransfer)
            }
        }
    }

    override fun subscribeState() {
        edcCardViewModel.checkCardResult.observe(this) {
            when (it?.status) {
                Constants.CARD_STATUS_UNSUPPORTED -> {
                    edcCardViewModel.stopCheckCard()
                    goToDestination(HomePageActivity::class.java)
                }

                Constants.CARD_STATUS_VALID -> {
                    edcCardViewModel.onEventReceived(
                        EdcCardViewModel.Event.OnStartCheckCard(
                            1, false
                        )
                    )
                    if (it.showErrorDialog) {
                        checkCardAndShowDialog()
                    }
                }
            }
        }
    }

    private fun checkCardAndShowDialog(){
        checkForCardRemove(this)
    }

    override fun onCardRemove() {
        goToDestination(HomePageActivity::class.java)
    }

    private fun trackBackPressEvent(buttonType: String) {
        val eventProperties = HashMap<String, String>()
        eventProperties[CardAnalyticsConstants.BUTTON] = buttonType
        eventProperties[CardAnalyticsConstants.TRANSACTION_TYPE] = CardAnalyticsConstants.TRANSFER
        eventProperties[CardAnalyticsConstants.PAGE] =
            CardAnalyticsConstants.MONEY_TRANSFER_DETAILS_CONFIRMATION_SCREEN
        Analytics.trackEvent(CardAnalyticsConstants.EVENT_BACK_BUTTON_CLICK, eventProperties)
    }
}