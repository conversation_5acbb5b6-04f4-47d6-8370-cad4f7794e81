package com.bukuwarung.edc.card.data.repository

import com.bukuwarung.edc.card.data.datasource.CheckBalanceDataSource
import com.bukuwarung.edc.card.data.model.CheckBalanceRequest
import javax.inject.Inject

class CheckBalanceRepository @Inject constructor(private val checkBalanceDataSource: CheckBalanceDataSource) {

    suspend fun fetchCardBalance(accountId: String, checkBalanceRequest: CheckBalanceRequest) =
        checkBalanceDataSource.fetchCardBalance(accountId, checkBalanceRequest)
}