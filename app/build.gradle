plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'kotlin-kapt'
    id 'com.google.dagger.hilt.android'
    id 'kotlin-parcelize'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
    id 'com.google.firebase.firebase-perf'
}

def versionMajor = 2
def versionMinor = 44
def versionPatch = 5

android {

    defaultConfig {
        applicationId "com.bukuwarung.bukuagen"
        minSdk 24
        compileSdk 34
        targetSdk 34
        versionCode versionMajor * 10000 + versionMinor * 100 + versionPatch
        versionName "${versionMajor}.${versionMinor}.${versionPatch}"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    dataBinding {
        enabled = true
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            resValue "string", "clear_text_config", "true"
        }
        release {
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            resValue "string", "clear_text_config", "false"
        }
    }
    sourceSets {
        main {
            java.srcDirs = ['src/main/java', 'src/main/aidl']
            aidl.srcDirs = ['src/main/aidl']
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        viewBinding true
    }
    lintOptions {
        checkReleaseBuilds false
        abortOnError false
    }

    flavorDimensions = ["store", "env"]
    productFlavors {
        play {
            dimension "store"
            applicationId "com.bukuwarung.bukuagen"
            isDefault true
            ndk {
                abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86', 'x86_64'
            }
        }
        veri {
            dimension "store"
            applicationId "com.bukuwarung.edc"
            ndk {
                abiFilters 'armeabi'
            }
        }
        pax {
            dimension "store"
            applicationId "com.bukuwarung.bukuagen"
            ndk {
                abiFilters 'armeabi'
            }
        }

        dev {
            dimension "env"
            versionNameSuffix "-DEV"
            applicationIdSuffix ".dev"
            buildConfigField("String", "DEEPLINK_URL", '"https://bukuwarungdev.page.link"')
            buildConfigField("String", "APP_CONFIG_VERSION", '"3.17.0"')
            buildConfigField("String", "DEEPLINK_SCHEME", '"intent://bukuwarungdev.page.link"')
            buildConfigField("String", "DEEPLINK_HOST", '"bukuwarungdev.page.link"')
            buildConfigField("String", "REPORT_URL_ID", '"https://bukuwarung.com/p?ae="')
            buildConfigField("String", "API_BASE_URL", '"https://api-dev.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_PAYMENT",'"https://api-dev.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_FINPRO",'"https://api-dev.bukuwarung.com/finpro/api/"')
            buildConfigField("String", "API_BASE_URL_LOYALTY",'"https://api-dev.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_RISK", '"https://api-dev.bukuwarung.com/risk/api/"')
            buildConfigField("String", "API_BASE_URL_BANKING", '"https://api-dev.bukuwarung.com/banking/services/external/"')
            buildConfigField("String", "ACCOUNTING_API_BASE_URL", '"https://api-dev.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_TWO_FA", '"https://api-dev.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_GOLDEN_GATE", '"https://api-dev.bukuwarung.com/golden-gate/api/"')
            buildConfigField("String", "API_BASE_URL_JANUS", '"https://api-dev.bukuwarung.com/janus/api/"')
            buildConfigField("String", "API_BASE_URL_TRANSACTIONS", '"https://api-dev.bukuwarung.com/transaction-history/api/"')
            buildConfigField("String", "API_BASE_URL_STREAKS", '"https://2yvdmltuje.execute-api.ap-southeast-1.amazonaws.com"')
            buildConfigField("String", "FORGOT_PIN_URL", '"https://api-dev.bukuwarung.com/payments-mweb/forgot-pin"')
            buildConfigField("String", "KYC_WEB_URL", '"https://api-dev.bukuwarung.com/payments-mweb/kyc/verify-prompt"')
            buildConfigField("String", "QRIS_WEB_URL", '"https://api-dev.bukuwarung.com/payments-mweb/qris/start"')
            buildConfigField("String", "QRIS_FORM_URL", '"https://api-dev.bukuwarung.com/payments-mweb/qris/form"')
            buildConfigField("String", "QRIS_BANK_URL", '"https://api-dev.bukuwarung.com/payments-mweb/qris/bank"')
            buildConfigField("String", "KYC_DOCS_URL", '"https://api-dev.bukuwarung.com/payments-mweb/kyc/verify-kk"')
            buildConfigField("String", "KYB_WEB_URL", '"https://api-dev.bukuwarung.com/payments-mweb/qris/kyb-docs"')
            buildConfigField("String", "APPEAL_FLOW_URL", '"https://api-dev.bukuwarung.com/payments-mweb/appeal/bank"')
            buildConfigField("String", "ASSIST_URL", '"https://api-dev.bukuwarung.com/payments-mweb/assist/"')
            buildConfigField("String", "ASSIST_TICKET_URL", '"https://api-dev.bukuwarung.com/payments-mweb/refund/request"')
            buildConfigField("String", "ASSIST_TICKET_PAGE_URL", '"https://api-dev.bukuwarung.com/payments-mweb/refund/assist-ticket/"')
            buildConfigField("String", "VOUCHER_GAME_URL", '"https://api-dev.bukuwarung.com/payments-mweb/vouchers/"')
            buildConfigField("String", "VOUCHER_GAME_PRODUCTS_URL", '"https://api-dev.bukuwarung.com/payments-mweb/voucher/"')
            buildConfigField("String", "LOS_WEB_LENDING_URL", '"https://api-dev.bukuwarung.com/los-web/landing"')
            buildConfigField("String", "LOS_WEB_BNPL_URL", '"https://api-dev.bukuwarung.com/los-web/bnpl"')
            buildConfigField("String", "VOUCHER_WEB_URL", '"https://api-dev.bukuwarung.com/mx-mweb/loyalty/vouchers/purchased/"')
            buildConfigField("String", "S3_BUCKET", '"https://bukuwarungac-image-dev.s3.ap-southeast-1.amazonaws.com"')
            buildConfigField("String", "TOKOKO_DEV_API", '"https://dev.tokowa.co/tokoko/"')
            buildConfigField("String", "ORIGIN", '"ANDROID"')
            buildConfigField("String", "PLATFORM", '"Play Store App"')
            buildConfigField("String", "MIXPANEL_TOKEN", '"4eeb93e63b515fc12d7a5d575306f4b0"')
            buildConfigField("String", "DEFAULT_MERCHANT_ID", '"718000100010001"')
            buildConfigField("String", "PAX_CLIENT_ID", '"RVSMT470H8SF86GPSTUG"')
            buildConfigField("String", "PAX_CLIENT_SECRET", '"G16KT5PHE2WS5GOHBJJS619WG10HN8H6OI4FUOBI"')
            buildConfigField("String", "BUKUWARUNG_EDC_ZOHO_APP_KEY", '"KtmWP7DO6QtG0i1vbhvqKKHSJl8%2BmnkL5ZBvuB9wGTA1MzgfVvOZZ7tgL3egmb5t"')
            buildConfigField("String", "BUKUWARUNG_EDC_ZOHO_ACCESS_KEY", '"Xzs0lywwEXg7JxuZLAcbiMPgRKbqWnr4QqgVkKOtxgFwJeIU5XH3xEUpRxGuT1Kt%2FRILH4cVxPC0p2yqLknCj62Mf8TjJZDr525%2FPH4V54cK%2BE2U7jqTGA%3D%3D"')
            buildConfigField("String", "BUKUWARUNG_BUKUAGEN_ZOHO_APP_KEY", '"KtmWP7DO6QueYN9i4KkbyjKLz%2B%2FWpVeO4RM2dRFfcocI6A8LiAL4GUFLSaEqDjNa"')
            buildConfigField("String", "BUKUWARUNG_BUKUAGEN_ZOHO_ACCESS_KEY", '"Xzs0lywwEXjQQ5gTt02PjVZj%2F1H4Av7TZs5KU1U0t%2BokIsKxU4X9fwo53t1jFol36AaF1zgZk9pFEhMmIH0m7U9AGrZEEGgIXA3%2B4JJtZyjuFrxBLDsGsuAcvXu6%2Bht%2B"')
            buildConfigField("String", "CLIENT_ID", '"2e3570c6-317e-4524-b284-980e5a4335b6"')
            buildConfigField("String", "CLIENT_SECRET", '"S81VsdrwNUN23YARAL54MFjB2JSV2TLn"')
            buildConfigField("String","EDC_DASHBOARD_URL",'"https://api-dev.bukuwarung.com/mx-mweb/edc/dashboard?entryPoint=BUKUAGEN"')
            buildConfigField("String","EDC_LANDING_URL",'"https://api-dev.bukuwarung.com/mx-mweb/edc/landing/external?entryPoint=BUKUAGEN"')
            buildConfigField("String","TIKTOK_APP_ID",'"7473416227906453505"')
            buildConfigField("String","MINI_ATMPRO_BUY_EDC_EXTERNAL_URL",'"https://api-dev.bukuwarung.com/mx-mweb/edc/landing/external"')
        }
        stg {
            dimension "env"
            versionNameSuffix "-STG"
            applicationIdSuffix ".staging"
            buildConfigField("String", "DEEPLINK_URL", '"https://bukuwarungstg.page.link"')
            buildConfigField("String", "DEEPLINK_SCHEME", '"intent://bukuwarungstg.page.link"')
            buildConfigField("String", "DEEPLINK_HOST", '"bukuwarungstg.page.link"')
            buildConfigField("String", "REPORT_URL_ID", '"https://bukuwarung.com/y?ae="')
            buildConfigField("String", "API_BASE_URL", '"https://api-staging-v1.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_PAYMENT",'"https://api-staging-v1.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_FINPRO",'"https://api-staging-v1.bukuwarung.com/finpro/api/"')
            buildConfigField("String", "API_BASE_URL_LOYALTY", '"https://api-staging-v1.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_RISK", '"https://api-staging-v1.bukuwarung.com/risk/api/"')
            buildConfigField("String", "API_BASE_URL_BANKING", '"https://api-staging-v1.bukuwarung.com/banking/services/external/"')
            buildConfigField("String", "API_BASE_URL_TWO_FA", '"https://api-staging-v1.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_GOLDEN_GATE", '"https://api-staging-v1.bukuwarung.com/golden-gate/api/"')
            buildConfigField("String", "API_BASE_URL_JANUS", '"https://api-staging-v1.bukuwarung.com/janus/api/"')
            buildConfigField("String", "API_BASE_URL_TRANSACTIONS", '"https://api-staging-v1.bukuwarung.com/transaction-history/api/"')
            buildConfigField("String", "ACCOUNTING_API_BASE_URL", '"https://api-staging-v1.bukuwarung.com"')
            buildConfigField("String", "APP_CONFIG_VERSION", '"3.17.0"')
            buildConfigField("String", "API_BASE_URL_STREAKS", '"https://xsvg63im27.execute-api.ap-southeast-1.amazonaws.com"')
            buildConfigField("String", "FORGOT_PIN_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/forgot-pin"')
            buildConfigField("String", "KYC_WEB_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/kyc/verify-prompt"')
            buildConfigField("String", "QRIS_WEB_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/qris/start"')
            buildConfigField("String", "QRIS_FORM_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/qris/form"')
            buildConfigField("String", "QRIS_BANK_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/qris/bank"')
            buildConfigField("String", "KYC_DOCS_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/kyc/verify-kk"')
            buildConfigField("String", "KYB_WEB_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/qris/kyb-docs"')
            buildConfigField("String", "APPEAL_FLOW_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/appeal/bank"')
            buildConfigField("String", "ASSIST_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/assist/"')
            buildConfigField("String", "ASSIST_TICKET_PAGE_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/refund/assist-ticket/"')
            buildConfigField("String", "ASSIST_TICKET_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/refund/request"')
            buildConfigField("String", "VOUCHER_GAME_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/vouchers/"')
            buildConfigField("String", "VOUCHER_GAME_PRODUCTS_URL", '"https://api-staging-v1.bukuwarung.com/payments-mweb/voucher/"')
            buildConfigField("String", "LOS_WEB_LENDING_URL", '"https://api-staging-v1.bukuwarung.com/los-web/landing"')
            buildConfigField("String", "LOS_WEB_BNPL_URL", '"https://api-staging-v1.bukuwarung.com/los-web/bnpl"')
            buildConfigField("String", "VOUCHER_WEB_URL", '"https://api-staging-v1.bukuwarung.com/mx-mweb/loyalty/vouchers/purchased/"')
            buildConfigField("String", "TOKOKO_DEV_API", '"https://dev.tokowa.co/tokoko/"')
            buildConfigField("String", "ORIGIN", '"ANDROID"')
            buildConfigField("String", "PLATFORM", '"Play Store App"')
            buildConfigField("String", "MIXPANEL_TOKEN", '"3fa727dc6d716f0b0dc8c5efae314277"')
            buildConfigField("String", "DEFAULT_MERCHANT_ID", '"718000100010001"')
            buildConfigField("String", "PAX_CLIENT_ID", '"RVSMT470H8SF86GPSTUG"')
            buildConfigField("String", "PAX_CLIENT_SECRET", '"G16KT5PHE2WS5GOHBJJS619WG10HN8H6OI4FUOBI"')
            buildConfigField("String", "BUKUWARUNG_EDC_ZOHO_APP_KEY", '"KtmWP7DO6QtG0i1vbhvqKKHSJl8%2BmnkL5ZBvuB9wGTA1MzgfVvOZZ7tgL3egmb5t"')
            buildConfigField("String", "BUKUWARUNG_EDC_ZOHO_ACCESS_KEY", '"Xzs0lywwEXg7JxuZLAcbiMPgRKbqWnr4QqgVkKOtxgFwJeIU5XH3xEUpRxGuT1Kt%2FRILH4cVxPC0p2yqLknCj62Mf8TjJZDr525%2FPH4V54cK%2BE2U7jqTGA%3D%3D"')
            buildConfigField("String", "BUKUWARUNG_BUKUAGEN_ZOHO_APP_KEY", '"KtmWP7DO6QueYN9i4KkbyjKLz%2B%2FWpVeO4RM2dRFfcocI6A8LiAL4GUFLSaEqDjNa"')
            buildConfigField("String", "BUKUWARUNG_BUKUAGEN_ZOHO_ACCESS_KEY", '"Xzs0lywwEXjQQ5gTt02PjVZj%2F1H4Av7TZs5KU1U0t%2BokIsKxU4X9fwo53t1jFol36AaF1zgZk9pFEhMmIH0m7U9AGrZEEGgIXA3%2B4JJtZyjuFrxBLDsGsuAcvXu6%2Bht%2B"')
            buildConfigField("String", "CLIENT_ID", '"2e3570c6-317e-4524-b284-980e5a4335b6"')
            buildConfigField("String", "CLIENT_SECRET", '"S81VsdrwNUN23YARAL54MFjB2JSV2TLn"')
            buildConfigField("String","EDC_DASHBOARD_URL",'"https://api-staging-v1.bukuwarung.com/mx-mweb/edc/dashboard?entryPoint=BUKUAGEN"')
            buildConfigField("String","EDC_LANDING_URL",'"https://api-staging-v1.bukuwarung.com/mx-mweb/edc/landing/external?entryPoint=BUKUAGEN"')
            buildConfigField("String","TIKTOK_APP_ID",'"7473416227906453505"')
            buildConfigField("String","MINI_ATMPRO_BUY_EDC_EXTERNAL_URL",'"https://api-staging-v1.bukuwarung.com/mx-mweb/edc/landing/external"')
        }
        prod {
            dimension "env"
            buildConfigField("String", "DEEPLINK_URL", '"https://bukuwarung.page.link"')
            buildConfigField("String", "DEEPLINK_SCHEME", '"intent://bukuwarung.page.link"')
            buildConfigField("String", "DEEPLINK_HOST", '"bukuwarung.page.link"')
            buildConfigField("String", "REPORT_URL_ID", '"https://bukuwarung.com/y?ae="')
            buildConfigField("String", "API_BASE_URL", '"https://api-v4.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_PAYMENT",'"https://api-v4.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_FINPRO",'"https://api-v4.bukuwarung.com/finpro/api/"')
            buildConfigField("String", "API_BASE_URL_LOYALTY", '"https://api-v4.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_RISK", '"https://api-v4.bukuwarung.com/risk/api/"')
            buildConfigField("String", "API_BASE_URL_BANKING", '"https://api-v4.bukuwarung.com/banking/services/external/"')
            buildConfigField("String", "API_BASE_URL_TWO_FA", '"https://api-v4.bukuwarung.com"')
            buildConfigField("String", "API_BASE_URL_GOLDEN_GATE", '"https://api-v4.bukuwarung.com/golden-gate/api/"')
            buildConfigField("String", "API_BASE_URL_JANUS", '"https://api-v4.bukuwarung.com/janus/api/"')
            buildConfigField("String", "API_BASE_URL_TRANSACTIONS", '"https://api-v4.bukuwarung.com/transaction-history/api/"')
            buildConfigField("String", "ACCOUNTING_API_BASE_URL", '"https://api-v4.bukuwarung.com"')
            buildConfigField("String", "APP_CONFIG_VERSION", '"3.60.0-bud"')
            buildConfigField("String", "API_BASE_URL_STREAKS", '"https://xsvg63im27.execute-api.ap-southeast-1.amazonaws.com"')
            buildConfigField("String", "FORGOT_PIN_URL", '"https://api-v4.bukuwarung.com/payments-mweb/forgot-pin"')
            buildConfigField("String", "KYC_WEB_URL", '"https://api-v4.bukuwarung.com/payments-mweb/kyc/verify-prompt"')
            buildConfigField("String", "QRIS_WEB_URL", '"https://api-v4.bukuwarung.com/payments-mweb/qris/start"')
            buildConfigField("String", "QRIS_FORM_URL", '"https://api-v4.bukuwarung.com/payments-mweb/qris/form"')
            buildConfigField("String", "QRIS_BANK_URL", '"https://api-v4.bukuwarung.com/payments-mweb/qris/bank"')
            buildConfigField("String", "KYC_DOCS_URL", '"https://api-v4.bukuwarung.com/payments-mweb/kyc/verify-kk"')
            buildConfigField("String", "KYB_WEB_URL", '"https://api-v4.bukuwarung.com/payments-mweb/qris/kyb-docs"')
            buildConfigField("String", "APPEAL_FLOW_URL", '"https://api-v4.bukuwarung.com/payments-mweb/appeal/bank"')
            buildConfigField("String", "ASSIST_URL", '"https://api-v4.bukuwarung.com/payments-mweb/assist/"')
            buildConfigField("String", "ASSIST_TICKET_PAGE_URL", '"https://api-v4.bukuwarung.com/payments-mweb/refund/assist-ticket/"')
            buildConfigField("String", "ASSIST_TICKET_URL", '"https://api-v4.bukuwarung.com/payments-mweb/refund/request"')
            buildConfigField("String", "VOUCHER_GAME_URL", '"https://api-v4.bukuwarung.com/payments-mweb/vouchers/"')
            buildConfigField("String", "VOUCHER_GAME_PRODUCTS_URL", '"https://api-v4.bukuwarung.com/payments-mweb/voucher/"')
            buildConfigField("String", "LOS_WEB_LENDING_URL", '"https://api-v4.bukuwarung.com/los-web/landing"')
            buildConfigField("String", "LOS_WEB_BNPL_URL", '"https://api-v4.bukuwarung.com/los-web/bnpl"')
            buildConfigField("String", "VOUCHER_WEB_URL", '"https://api-v4.bukuwarung.com/mx-mweb/loyalty/vouchers/purchased/"')
            buildConfigField("String", "TOKOKO_DEV_API", '"https://dev.tokowa.co/tokoko/"')
            buildConfigField("String", "ORIGIN", '"ANDROID"')
            buildConfigField("String", "PLATFORM", '"Play Store App"')
            buildConfigField("String", "MIXPANEL_TOKEN", '"704d441f5fd99d04f5f547c4323446c8"')
            buildConfigField("String", "DEFAULT_MERCHANT_ID", '"718039455370001"')
            buildConfigField("String", "PAX_CLIENT_ID", '"R41Q34DF3X4S3PZ4D3LT"')
            buildConfigField("String", "PAX_CLIENT_SECRET", '"6YS0JF8VLCJYPS0KMZ4F8KDYV0R7SQUJ6VZ9NKD0"')
            buildConfigField("String", "BUKUWARUNG_EDC_ZOHO_APP_KEY", '"KtmWP7DO6QtG0i1vbhvqKKHSJl8%2BmnkL5ZBvuB9wGTA1MzgfVvOZZ7tgL3egmb5t"')
            buildConfigField("String", "BUKUWARUNG_EDC_ZOHO_ACCESS_KEY", '"Xzs0lywwEXg7JxuZLAcbiMPgRKbqWnr4QqgVkKOtxgFwJeIU5XH3xEUpRxGuT1Kt%2FRILH4cVxPC0p2yqLknCj62Mf8TjJZDr525%2FPH4V54cK%2BE2U7jqTGA%3D%3D"')
            buildConfigField("String", "BUKUWARUNG_BUKUAGEN_ZOHO_APP_KEY", '"KtmWP7DO6QueYN9i4KkbyjKLz%2B%2FWpVeO4RM2dRFfcocI6A8LiAL4GUFLSaEqDjNa"')
            buildConfigField("String", "BUKUWARUNG_BUKUAGEN_ZOHO_ACCESS_KEY", '"Xzs0lywwEXjQQ5gTt02PjVZj%2F1H4Av7TZs5KU1U0t%2BokIsKxU4X9fwo53t1jFol36AaF1zgZk9pFEhMmIH0m7U9AGrZEEGgIXA3%2B4JJtZyjuFrxBLDsGsuAcvXu6%2Bht%2B"')
            buildConfigField("String", "CLIENT_ID", '"2e3570c6-317e-4524-b284-980e5a4335b6"')
            buildConfigField("String", "CLIENT_SECRET", '"S81VsdrwNUN23YARAL54MFjB2JSV2TLn"')
            buildConfigField("String","EDC_DASHBOARD_URL",'"https://api-v4.bukuwarung.com/mx-mweb/edc/dashboard?entryPoint=BUKUAGEN"')
            buildConfigField("String","EDC_LANDING_URL",'"https://api-v4.bukuwarung.com/mx-mweb/edc/landing/external?entryPoint=BUKUAGEN"')
            buildConfigField("String","TIKTOK_APP_ID",'"7473416227906453505"')
            buildConfigField("String","MINI_ATMPRO_BUY_EDC_EXTERNAL_URL",'"https://api-v4.bukuwarung.com/mx-mweb/edc/landing/external"')
        }
    }

    gradle.taskGraph.beforeTask { Task task ->
        if (task.name ==~ /process.*GoogleServices/) {
            android.applicationVariants.configureEach { variant ->
                def build = variant.buildType.name
                def store = variant.productFlavors[0].name
                def env = variant.productFlavors[1].name

                if (task.name ==~ /(?i)process${store}${env}${build}GoogleServices/) {
                    copy {
                        from "src/$env/"
                        include "google-services.json"
                        into "."
                    }
                }
            }
        }
    }
}

dependencies {
    implementation project(':ui-component')
    implementation "com.github.razir.progressbutton:progressbutton:2.1.0"
    implementation "com.facebook.shimmer:shimmer:0.5.0"

    implementation fileTree(include: ['*.jar', '*.aar'], dir: 'libs')

    implementation(platform("org.jetbrains.kotlin:kotlin-bom:1.8.0"))

    implementation 'androidx.core:core-ktx:1.7.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.10.0'
    implementation 'androidx.annotation:annotation:1.7.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.6.2'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2'
    implementation "androidx.fragment:fragment-ktx:1.6.1"
    implementation "androidx.activity:activity-ktx:1.8.0"

    // retrofit
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:okhttp:4.9.1'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.9.1'

    // glide
    implementation 'com.github.bumptech.glide:glide:4.14.2'
    implementation 'androidx.work:work-runtime-ktx:2.7.1'
    implementation 'com.google.android.gms:play-services-maps:18.0.0'
    implementation 'androidx.browser:browser:1.6.0'
    implementation 'androidx.databinding:databinding-common:8.2.2'
    implementation 'androidx.databinding:databinding-adapters:8.2.2'
    implementation 'com.google.firebase:firebase-firestore-ktx:20.2.0'
    //implementation files('libs/ysdk_5.95.541575f_24121210.jar')
    implementation files('libs/sun.misc.BASE64Decoder.jar')
    implementation files('libs/bcprov-jdk15on-160.jar')
    annotationProcessor 'com.github.bumptech.glide:compiler:4.14.2'

    //app -update
    implementation 'com.google.android.play:app-update-ktx:2.1.0'

    // oauth jwt
    implementation 'com.auth0.android:jwtdecode:2.0.1'

    // dagger hilt
    implementation 'com.google.dagger:hilt-android:2.44'
    kapt 'com.google.dagger:hilt-compiler:2.44'
    implementation 'androidx.hilt:hilt-work:1.0.0'
    kapt 'androidx.hilt:hilt-compiler:1.0.0'

    // location service
    implementation 'com.google.android.gms:play-services-location:20.0.0'

    implementation 'com.airbnb.android:lottie:3.4.2'

    testImplementation 'junit:junit:4.13.2'

    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'

    // Remote config
    implementation(platform("com.google.firebase:firebase-bom:32.3.1"))
    implementation 'com.google.firebase:firebase-config-ktx'
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation "com.google.firebase:firebase-messaging"

    // Animation
    implementation 'com.airbnb.android:lottie:3.4.2'

    // Pagination
    implementation "androidx.paging:paging-runtime-ktx:3.2.1"

    implementation 'com.github.mrmike:ok2curl:0.8.0'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'
    implementation 'io.github.pilgr:paperdb:2.7.1'

    // paxstore
    implementation ('com.whatspos.sdk:paxstore-3rd-app-android-sdk:9.3.0'){
        exclude group: 'com.google.code.gson', module: 'gson'
    }
//    implementation project(':sdk')
    // Crashlytics
    implementation("com.google.firebase:firebase-crashlytics")
    implementation("com.google.firebase:firebase-analytics")

    implementation 'com.google.firebase:firebase-auth:21.0.3'
    implementation 'com.google.android.gms:play-services-auth:19.2.0'

    // Mixpanel
    implementation 'com.mixpanel.android:mixpanel-android:7.5.2'
    implementation project(':bluetooth-devices-setup')
//    implementation project(':neuro')

    // Performance
    implementation("com.google.firebase:firebase-perf")
    implementation "com.google.firebase:firebase-appcheck-playintegrity:16.0.0-beta01"

    implementation 'com.zoho.salesiq:mobilisten:4.3.3'
    implementation "androidx.security:security-crypto:1.0.0"

    implementation 'com.google.mlkit:face-detection:16.1.5'
    implementation "com.squareup.retrofit2:adapter-rxjava2:2.9.0"
    implementation "io.reactivex.rxjava2:rxjava:2.2.19"

    implementation "androidx.camera:camera-camera2:1.1.0-beta02"
    implementation "androidx.camera:camera-lifecycle:1.1.0-beta02"
    implementation "androidx.camera:camera-view:1.1.0-beta02"
    implementation "androidx.camera:camera-video:1.1.0-beta02"
    implementation "id.zelory:compressor:3.0.1"

    //tiktok
    implementation "com.github.tiktok:tiktok-business-android-sdk:1.3.5"
}
// Custom Gradle task to print the version name
task printVersionName {
    doLast {
        // Access the versionName from the defaultConfig block in the android configuration
        println "v${android.defaultConfig.versionName}"
    }
}
